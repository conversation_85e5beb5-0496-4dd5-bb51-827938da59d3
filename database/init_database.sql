-- Creative Storyboard Database Initialization Script
-- This script creates all necessary tables for the application

-- Create database if it doesn't exist (uncomment if needed)
-- CREATE DATABASE IF NOT EXISTS creative_storyboard;
-- USE creative_storyboard;

-- Create users table (if it doesn't already exist)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing user ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique username for login',
    email VARCHAR(255) NOT NULL UNIQUE COMMENT 'Unique email address for login',
    password VARCHAR(255) NOT NULL COMMENT 'Hashed password using bcrypt',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when user account was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when user account was last updated'
) COMMENT = 'Stores user account information for authentication and authorization';

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing project ID',
    user_id INT NOT NULL COMMENT 'Foreign key reference to users.id - owner of the project',
    title VARCHAR(128) NOT NULL COMMENT 'Project title, maximum 128 characters',
    description VARCHAR(4000) COMMENT 'Project description, maximum 4000 characters',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when project was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when project was last updated',
    image VARCHAR(256) COMMENT 'File path to project image uploaded by user, maximum 256 characters',

    -- Foreign key constraint to users table
    CONSTRAINT fk_projects_user_id
        FOREIGN KEY (user_id)
        REFERENCES users(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    -- Indexes for better query performance
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_title (title)
) COMMENT = 'Stores project information created by users in the Creative Storyboard application';

-- Create scenes table
CREATE TABLE IF NOT EXISTS scenes (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing scene ID',
    project_id INT NOT NULL COMMENT 'Foreign key reference to projects.id - parent project',
    title VARCHAR(128) NOT NULL COMMENT 'Scene title, maximum 128 characters',
    description VARCHAR(4000) COMMENT 'Scene description, maximum 4000 characters',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when scene was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when scene was last updated',

    -- Foreign key constraint to projects table
    CONSTRAINT fk_scenes_project_id
        FOREIGN KEY (project_id)
        REFERENCES projects(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    -- Indexes for better query performance
    INDEX idx_project_id (project_id),
    INDEX idx_created_at (created_at),
    INDEX idx_title (title)
) COMMENT = 'Stores scene information for each project in the Creative Storyboard application';

-- Create shots table
CREATE TABLE IF NOT EXISTS shots (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing shot ID',
    scene_id INT NOT NULL COMMENT 'Foreign key reference to scenes.id - parent scene',
    title VARCHAR(128) NOT NULL COMMENT 'Shot title, maximum 128 characters',
    content TEXT COMMENT 'Detailed shot content and description',
    ai_prompt TEXT COMMENT 'AI prompt used for generating shot content or images',
    ordering INT NOT NULL DEFAULT 1 COMMENT 'Order of the shot within the scene, starts from 1',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when shot was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when shot was last updated',

    -- Foreign key constraint to scenes table
    CONSTRAINT fk_shots_scene_id
        FOREIGN KEY (scene_id)
        REFERENCES scenes(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    -- Indexes for better query performance
    INDEX idx_scene_id (scene_id),
    INDEX idx_ordering (ordering),
    INDEX idx_scene_ordering (scene_id, ordering),
    INDEX idx_created_at (created_at),
    INDEX idx_title (title)
) COMMENT = 'Stores individual shots for each scene in the Creative Storyboard application';

-- Create shot_content table
CREATE TABLE IF NOT EXISTS shot_content (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing shot content ID',
    shot_id INT NOT NULL COMMENT 'Foreign key reference to shots.id - parent shot',
    content_type ENUM('image', 'audio', 'video') NOT NULL COMMENT 'Type of content: image, audio, or video',
    file_path VARCHAR(255) NOT NULL COMMENT 'File path where the content is stored, maximum 255 characters',
    notes VARCHAR(255) NULL COMMENT 'Optional notes about the shot, maximum 255 characters',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when shot content was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when shot content was last updated',

    -- Foreign key constraint to shots table
    CONSTRAINT fk_shot_content_shot_id
        FOREIGN KEY (shot_id)
        REFERENCES shots(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    -- Indexes for better query performance
    INDEX idx_shot_id (shot_id),
    INDEX idx_content_type (content_type),
    INDEX idx_shot_content_type (shot_id, content_type),
    INDEX idx_created_at (created_at)
) COMMENT = 'Stores media files (images, audio, video) associated with each shot in the Creative Storyboard application';

-- Create activities table
CREATE TABLE IF NOT EXISTS activities (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing activity ID',
    project_id INT NULL COMMENT 'Optional foreign key reference to projects.id',
    scene_id INT NULL COMMENT 'Optional foreign key reference to scenes.id',
    shot_id INT NULL COMMENT 'Optional foreign key reference to shots.id',
    shot_content_id INT NULL COMMENT 'Optional foreign key reference to shot_content.id',
    user_id INT NOT NULL COMMENT 'Foreign key reference to users.id - who performed the action',
    activity VARCHAR(255) NOT NULL COMMENT 'Description of the user activity',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when the activity happened',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when the activity row was last updated',

    -- Foreign key constraints; keep activity log even if entities are deleted
    CONSTRAINT fk_activities_project_id
        FOREIGN KEY (project_id)
        REFERENCES projects(id)
        ON DELETE SET NULL
        ON UPDATE CASCADE,

    CONSTRAINT fk_activities_scene_id
        FOREIGN KEY (scene_id)
        REFERENCES scenes(id)
        ON DELETE SET NULL
        ON UPDATE CASCADE,

    CONSTRAINT fk_activities_shot_id
        FOREIGN KEY (shot_id)
        REFERENCES shots(id)
        ON DELETE SET NULL
        ON UPDATE CASCADE,

    CONSTRAINT fk_activities_shot_content_id
        FOREIGN KEY (shot_content_id)
        REFERENCES shot_content(id)
        ON DELETE SET NULL
        ON UPDATE CASCADE,

    CONSTRAINT fk_activities_user_id
        FOREIGN KEY (user_id)
        REFERENCES users(id)
        ON DELETE RESTRICT
        ON UPDATE CASCADE,

    -- Helpful indexes for filtering and ordering
    INDEX idx_user_id (user_id),
    INDEX idx_project_id (project_id),
    INDEX idx_scene_id (scene_id),
    INDEX idx_shot_id (shot_id),
    INDEX idx_shot_content_id (shot_content_id),
    INDEX idx_created_at (created_at)
) COMMENT = 'Tracks user activities across projects, scenes, shots, and media';

-- Display success message
SELECT 'Database tables created successfully!' as message;
