# Database Scripts

This folder contains SQL scripts for setting up and managing the Creative Storyboard database.

## Files

### `init_database.sql`
Complete database initialization script that creates all necessary tables:
- `users` table - stores user account information
- `projects` table - stores project information created by users

This single script handles the entire database setup with proper foreign key constraints, indexes, and documentation.

## Usage

### Running the Database Setup
Execute the initialization script to create all tables:
```bash
mysql -u your_username -p your_database_name < database/init_database.sql
```

### Using MySQL Workbench or phpMyAdmin
1. Open your MySQL client
2. Select your database
3. Copy and paste the contents of the desired SQL file
4. Execute the script

## Environment Variables

Make sure your `.env` file contains the following database configuration:
```
DB_HOST=localhost
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password
DB_NAME=your_database_name
```

## Projects Table Schema

| Column | Type | Description |
|--------|------|-------------|
| `id` | INT AUTO_INCREMENT | Primary key |
| `user_id` | INT | Foreign key to users.id (project owner) |
| `title` | VARCHAR(128) | Project title |
| `description` | VARCHAR(4000) | Project description |
| `created_at` | TIMESTAMP | Creation timestamp |
| `updated_at` | TIMESTAMP | Last update timestamp |
| `image` | VARCHAR(256) | Path to project image file |

## Features

- **Foreign Key Constraints**: Ensures data integrity between users and projects
- **Cascading Deletes**: When a user is deleted, their projects are automatically deleted
- **Indexes**: Optimized for common queries on user_id, created_at, and title
- **Timestamps**: Automatic creation and update timestamps
- **Documentation**: Comprehensive comments on tables and columns

## Notes

- The scripts use `CREATE TABLE IF NOT EXISTS` to prevent errors if tables already exist
- All timestamps use MySQL's `CURRENT_TIMESTAMP` function
- The `image` field stores file paths, not binary data
- Foreign key constraints ensure referential integrity
