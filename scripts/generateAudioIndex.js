const fs = require('fs');
const path = require('path');

const ROOT = path.resolve(__dirname, '..', 'collection', 'audio');
const OUT = path.resolve(__dirname, '..', 'collection', 'audio', 'index.json');
const AUDIO_EXT = new Set(['.wav', '.mp3', '.ogg', '.m4a', '.aac', '.flac', '.webm']);

function isAudio(p) {
  return AUDIO_EXT.has(path.extname(p).toLowerCase());
}

function titleFromFilename(name) {
  // Remove extension
  const base = name.replace(/\.[^.]+$/, '');
  // Split on non-letters; keep tokens with letters; drop pure numbers
  const tokens = base
    .split(/[^a-zA-Z]+/)
    .filter(t => t && /[a-zA-Z]/.test(t));
  const s = tokens.join(' ').toLowerCase();
  return s ? s.charAt(0).toUpperCase() + s.slice(1) : name;
}

function indexString(title) {
  return (title || '').toLowerCase();
}

function walk(dir, relBase = '') {
  const out = [];
  for (const entry of fs.readdirSync(dir, { withFileTypes: true })) {
    if (entry.name.startsWith('.')) continue; // skip dotfiles
    const abs = path.join(dir, entry.name);
    const rel = path.join(relBase, entry.name);
    if (entry.isDirectory()) {
      out.push(...walk(abs, rel));
    } else if (entry.isFile() && isAudio(abs)) {
      out.push({ relPath: rel });
    }
  }
  return out;
}

function main() {
  if (!fs.existsSync(ROOT)) {
    console.error('Audio root not found:', ROOT);
    process.exit(1);
  }
  const files = walk(ROOT);
  const items = files.map(({ relPath }) => {
    const name = path.basename(relPath);
    const title = titleFromFilename(name);
    return {
      title,
      path: relPath.replace(/\\/g, '/'), // normalize
      type: 'sfx',
      index: indexString(title)
    };
  });
  // Sort by title for stable order
  items.sort((a, b) => a.title.localeCompare(b.title));
  fs.writeFileSync(OUT, JSON.stringify(items, null, 2));
  console.log(`Wrote ${items.length} items to`, OUT);
}

main();

