import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../components/Navbar';
import { useTheme } from '../contexts/ThemeContext';

export default function ProfilePage() {
  const [user, setUser] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    bio: '',
    location: '',
    website: ''
  });
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (res.ok) {
          const data = await res.json();
          setUser(data);
          setFormData({
            username: data.username || '',
            email: data.email || '',
            bio: data.bio || '',
            location: data.location || '',
            website: data.website || ''
          });
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [router]);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSave = async (e) => {
    e.preventDefault();
    try {
      const res = await fetch('http://localhost:3001/api/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(formData)
      });
      
      const data = await res.json();
      setMessage(data.message);
      
      if (res.ok) {
        setUser({ ...user, ...formData });
        setIsEditing(false);
      }
    } catch (error) {
      setMessage('Error updating profile');
    }
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container" style={{ paddingTop: '100px' }}>
        <div className="profile-container">
          <div className="profile-header">
            <div className="avatar">
              {user?.username ? user.username.charAt(0).toUpperCase() : '?'}
            </div>
            <h1>{user?.username || 'User'}</h1>
            <p className="user-email">{user?.email}</p>
            
            <div className="profile-stats">
              <div className="stat-item">
                <span className="stat-number">12</span>
                <span className="stat-label">Projects</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">48</span>
                <span className="stat-label">Storyboards</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">156</span>
                <span className="stat-label">Scenes</span>
              </div>
            </div>
          </div>

          <div className="profile-content">
            <div className="profile-section">
              <div className="section-header">
                <h2>Profile Information</h2>
                <button 
                  className="btn-secondary"
                  onClick={() => setIsEditing(!isEditing)}
                >
                  {isEditing ? (<><i className="fa-solid fa-xmark" style={{ marginRight: 6 }}></i> Cancel</>) : (<><i className="fa-solid fa-pen" style={{ marginRight: 6 }}></i> Edit</>)}
                </button>
              </div>

              {isEditing ? (
                <form onSubmit={handleSave} className="profile-form">
                  <div className="form-group">
                    <label>Username</label>
                    <input
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>Email</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>Bio</label>
                    <textarea
                      name="bio"
                      value={formData.bio}
                      onChange={handleInputChange}
                      placeholder="Tell us about yourself..."
                      rows="4"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>Location</label>
                    <input
                      type="text"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      placeholder="City, Country"
                    />
                  </div>
                  
                  <div className="form-group">
                    <label>Website</label>
                    <input
                      type="url"
                      name="website"
                      value={formData.website}
                      onChange={handleInputChange}
                      placeholder="https://yourwebsite.com"
                    />
                  </div>
                  
                  <button type="submit"><i className="fa-solid fa-floppy-disk" style={{ marginRight: 6 }}></i> Save Changes</button>
                </form>
              ) : (
                <div className="profile-info">
                  <div className="info-item">
                    <strong>Bio:</strong>
                    <p>{user?.bio || 'No bio added yet.'}</p>
                  </div>
                  <div className="info-item">
                    <strong>Location:</strong>
                    <p>{user?.location || 'Not specified'}</p>
                  </div>
                  <div className="info-item">
                    <strong>Website:</strong>
                    <p>{user?.website ? <a href={user.website} target="_blank" rel="noopener noreferrer">{user.website}</a> : 'Not specified'}</p>
                  </div>
                </div>
              )}
              
              {message && (
                <div className={`message ${message.includes('Error') ? 'error' : 'success'}`}>
                  {message}
                </div>
              )}
            </div>

            <div className="profile-section">
              <h2>Account Settings</h2>
              <div className="settings-grid">
                <div className="setting-item">
                  <span><i className="fa-regular fa-bell" style={{ marginRight: 8 }}></i> Notifications</span>
                  <label className="switch">
                    <input type="checkbox" defaultChecked />
                    <span className="slider"></span>
                  </label>
                </div>
                <div className="setting-item">
                  <span><i className="fa-solid fa-palette" style={{ marginRight: 8 }}></i> Theme</span>
                  <select
                    value={theme}
                    onChange={(e) => setTheme(e.target.value)}
                    className="profile-theme-dropdown"
                  >
                    <option value="system">System</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </select>
                </div>
                <div className="setting-item">
                  <span><i className="fa-regular fa-envelope" style={{ marginRight: 8 }}></i> Email Updates</span>
                  <label className="switch">
                    <input type="checkbox" defaultChecked />
                    <span className="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
