import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../../components/Navbar';
import Modal from '../../components/Modal';
import Tooltip from '../../components/Tooltip';

export default function ProjectDetailPage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSceneModal, setShowSceneModal] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image: null
  });
  const [imagePreview, setImagePreview] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [scenes, setScenes] = useState([]);
  const [sceneFormData, setSceneFormData] = useState({
    title: '',
    description: ''
  });
  const [sceneSubmitting, setSceneSubmitting] = useState(false);
  const [sceneMessage, setSceneMessage] = useState('');
  const [editingScene, setEditingScene] = useState(null);
  const [isEditingScene, setIsEditingScene] = useState(false);
  const [sceneSort, setSceneSort] = useState('created_desc');
  const [dangerOpen, setDangerOpen] = useState(false);
  const [deleteTitleInput, setDeleteTitleInput] = useState('');
  const [deleting, setDeleting] = useState(false);
  const [deleteMsg, setDeleteMsg] = useState('');
  const router = useRouter();
  const { id } = router.query;

  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      try {
        // Fetch user data
        const userRes = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (userRes.ok) {
          const userData = await userRes.json();
          setUser(userData);
          
          // Fetch project details
          const projectRes = await fetch(`http://localhost:3001/api/projects/${id}`, {
            credentials: 'include'
          });
          if (projectRes.ok) {
            const projectData = await projectRes.json();
            setProject(projectData.project);

            // Fetch scenes for this project
            const scenesRes = await fetch(`http://localhost:3001/api/projects/${id}/scenes`, {
              credentials: 'include'
            });
            if (scenesRes.ok) {
              const scenesData = await scenesRes.json();
              setScenes(scenesData.scenes);
            }
          } else {
            router.push('/projects');
          }
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, router]);

  // Load persisted scene sort preference per project on mount/id change
  useEffect(() => {
    if (!id) return;
    try {
      const key = `sceneSort:${id}`;
      const saved = typeof window !== 'undefined' ? window.localStorage.getItem(key) : null;
      const allowed = ['created_desc', 'created_asc', 'updated_desc', 'updated_asc'];
      if (saved && allowed.includes(saved)) {
        setSceneSort(saved);
      }
    } catch (_) {
      // ignore read errors (e.g., privacy mode)
    }
  }, [id]);

  // Persist scene sort preference whenever it changes
  useEffect(() => {
    if (!id) return;
    try {
      const key = `sceneSort:${id}`;
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, sceneSort);
      }
    } catch (_) {
      // ignore write errors
    }
  }, [id, sceneSort]);

  const sortedScenes = useMemo(() => {
    const arr = Array.isArray(scenes) ? [...scenes] : [];
    const getTime = (v) => (v ? new Date(v).getTime() : 0);
    switch (sceneSort) {
      case 'created_asc':
        return arr.sort((a, b) => getTime(a.created_at) - getTime(b.created_at));
      case 'updated_desc':
        return arr.sort((a, b) => getTime(b.updated_at) - getTime(a.updated_at));
      case 'updated_asc':
        return arr.sort((a, b) => getTime(a.updated_at) - getTime(b.updated_at));
      case 'created_desc':
      default:
        return arr.sort((a, b) => getTime(b.created_at) - getTime(a.created_at));
    }
  }, [scenes, sceneSort]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        image: file
      }));
      
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleEditProject = () => {
    setFormData({
      title: project.title,
      description: project.description || '',
      image: null
    });
    
    if (project.image && project.image.startsWith('/uploads')) {
      setImagePreview(`http://localhost:3001${project.image}`);
    } else {
      setImagePreview(null);
    }
    
    setShowEditModal(true);
  };

  const handleUpdateProject = async (e) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      setMessage('Project title is required');
      return;
    }

    setSubmitting(true);
    setMessage('');

    try {
      const formDataToSend = new FormData();
      formDataToSend.append('title', formData.title.trim());
      formDataToSend.append('description', formData.description.trim());
      
      if (formData.image) {
        formDataToSend.append('image', formData.image);
      } else {
        formDataToSend.append('keepCurrentImage', 'true');
      }

      const res = await fetch(`http://localhost:3001/api/projects/${id}`, {
        method: 'PUT',
        credentials: 'include',
        body: formDataToSend
      });

      const data = await res.json();
      
      if (res.ok) {
        setMessage('Project updated successfully!');
        
        // Update project state
        setProject(prev => ({
          ...prev,
          title: formData.title.trim(),
          description: formData.description.trim(),
          image: data.imagePath || prev.image
        }));
        
        // Reset form
        setFormData({ title: '', description: '', image: null });
        setImagePreview(null);
        
        setTimeout(() => {
          setShowEditModal(false);
          setMessage('');
        }, 1500);
      } else {
        setMessage(data.message || 'Failed to update project');
      }
    } catch (error) {
      console.error('Error updating project:', error);
      setMessage('Connection error. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteProject = async () => {
    if (!project) return;
    if (deleteTitleInput.trim() !== project.title.trim()) {
      setDeleteMsg('Project title does not match.');
      return;
    }
    const confirmed = window.confirm('Are you sure you want to delete this project? The data will be lost and cannot be reverted.');
    if (!confirmed) return;

    setDeleting(true);
    setDeleteMsg('');
    try {
      const res = await fetch(`http://localhost:3001/api/projects/${id}`, {
        method: 'DELETE',
        credentials: 'include'
      });
      const data = await res.json();
      if (res.ok) {
        setDeleteMsg('Project deleted successfully. Redirecting...');
        setTimeout(() => {
          router.push('/home');
        }, 1000);
      } else {
        setDeleteMsg(data.message || 'Failed to delete project');
      }
    } catch (err) {
      console.error('Delete project error:', err);
      setDeleteMsg('Connection error. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  const handleSceneInputChange = (e) => {
    const { name, value } = e.target;
    setSceneFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEditScene = (scene) => {
    setEditingScene(scene);
    setIsEditingScene(true);
    setSceneFormData({
      title: scene.title,
      description: scene.description || ''
    });
    setShowSceneModal(true);
  };

  const handleCreateScene = async (e) => {
    e.preventDefault();

    if (!sceneFormData.title.trim()) {
      setSceneMessage('Scene title is required');
      return;
    }

    setSceneSubmitting(true);
    setSceneMessage('');

    try {
      const url = isEditingScene
        ? `http://localhost:3001/api/projects/${id}/scenes/${editingScene.id}`
        : `http://localhost:3001/api/projects/${id}/scenes`;

      const method = isEditingScene ? 'PUT' : 'POST';

      const res = await fetch(url, {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          title: sceneFormData.title.trim(),
          description: sceneFormData.description.trim()
        })
      });

      const data = await res.json();

      if (res.ok) {
        setSceneMessage(isEditingScene ? 'Scene updated successfully!' : 'Scene created successfully!');

        // Reset form
        setSceneFormData({ title: '', description: '' });
        setEditingScene(null);
        setIsEditingScene(false);

        // Refresh scenes list
        const scenesRes = await fetch(`http://localhost:3001/api/projects/${id}/scenes`, {
          credentials: 'include'
        });
        if (scenesRes.ok) {
          const scenesData = await scenesRes.json();
          setScenes(scenesData.scenes);
        }

        // Close modal after a short delay
        setTimeout(() => {
          setShowSceneModal(false);
          setSceneMessage('');
        }, 1500);
      } else {
        setSceneMessage(data.message || `Failed to ${isEditingScene ? 'update' : 'create'} scene`);
      }
    } catch (error) {
      console.error(`Error ${isEditingScene ? 'updating' : 'creating'} scene:`, error);
      setSceneMessage('Connection error. Please try again.');
    } finally {
      setSceneSubmitting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  if (!project) {
    return (
      <>
        <Navbar />
        <div className="container" style={{ paddingTop: '100px' }}>
          <div className="error-state">
            <h2>Project not found</h2>
            <button className="cta-primary" onClick={() => router.push('/projects')}>
              ← Back to Projects
            </button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container" style={{ paddingTop: '100px' }}>
      <div className="project-detail-container">
          {/* Project Header */}
          <div className="project-detail-header">
            <button className="back-btn" onClick={() => router.push('/projects')}>
              <i className="fa-solid fa-arrow-left" style={{ marginRight: 6 }}></i> Back to Projects
            </button>
            
            <div className="project-actions">
              <Tooltip text="Edit Project">
                <button className="btn-secondary" onClick={handleEditProject}>
                  <i className="fa-solid fa-pen" style={{ marginRight: 6 }}></i> Edit Project
                </button>
              </Tooltip>
            </div>
          </div>

          {/* Project Banner */}
          <div className="project-banner">
            {project.image && project.image.startsWith('/uploads') ? (
              <img 
                src={`http://localhost:3001${project.image}`} 
                alt={project.title}
                className="banner-image"
              />
            ) : project.image && project.image.startsWith('linear-gradient') ? (
              <div 
                className="banner-pattern"
                style={{ background: project.image }}
              ></div>
            ) : (
              <div className="banner-pattern" style={{ 
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
              }}></div>
            )}
            
            <div className="banner-overlay">
              <div className="project-info">
                <h1>{project.title}</h1>
                <div className="project-meta-info">
                  <span className="meta-badge">
                    <i className="fa-solid fa-masks-theater" style={{ marginRight: 6 }}></i> {scenes.length} scenes
                  </span>
                  <span className="meta-badge">
                    <i className="fa-regular fa-calendar" style={{ marginRight: 6 }}></i> Created {formatDate(project.created_at)}
                  </span>
                  <span className="meta-badge">
                    <i className="fa-regular fa-clock" style={{ marginRight: 6 }}></i> Updated {formatDate(project.updated_at)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Project Content */}
          <div className="project-detail-content">
            <div className="content-section">
              <h2>Description</h2>
              <p className="project-description-full">
                {project.description || 'No description provided for this project.'}
              </p>
            </div>

            <div className="content-section">
              <div className="section-header" style={{ gap: 12, alignItems: 'flex-start', flexDirection: 'column' }}>
                <div style={{ width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <h2>Scenes ({scenes.length})</h2>
                  <button className="cta-primary" onClick={() => {
                  setIsEditingScene(false);
                  setEditingScene(null);
                  setSceneFormData({ title: '', description: '' });
                  setSceneMessage('');
                  setShowSceneModal(true);
                }}>
                  <i className="fa-solid fa-plus" style={{ marginRight: 8 }}></i> Create Scene
                </button>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8, alignSelf: 'flex-end', marginTop: 8 }}>
                  <select
                    id="scene-sort"
                    value={sceneSort}
                    onChange={(e) => setSceneSort(e.target.value)}
                    className="scene-sort-dropdown"
                  >
                    <option value="created_desc">Show newly created first</option>
                    <option value="created_asc">Show oldest created first</option>
                    <option value="updated_desc">Show latest updated first</option>
                    <option value="updated_asc">Show oldest updated first</option>
                  </select>
                </div>
              </div>
              
              {scenes.length === 0 ? (
                <div className="empty-scenes">
                  <div className="empty-icon"><i className="fa-solid fa-clapperboard"></i></div>
                  <h3>No scenes yet</h3>
                  <p>Start building your storyboard by creating your first scene!</p>
                  <button className="cta-primary" onClick={() => {
                    setIsEditingScene(false);
                    setEditingScene(null);
                    setSceneFormData({ title: '', description: '' });
                    setSceneMessage('');
                    setShowSceneModal(true);
                  }}>
                    <i className="fa-solid fa-clapperboard" style={{ marginRight: 8 }}></i> Create First Scene
                  </button>
                </div>
              ) : (
                <div className="scenes-grid">
                  {sortedScenes.map((scene, index) => (
                    <div
                      key={scene.id}
                      className="scene-card"
                      role="button"
                      tabIndex={0}
                      style={{ cursor: 'pointer' }}
                      onClick={() => router.push(`/projects/${id}/scenes/${scene.id}`)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          router.push(`/projects/${id}/scenes/${scene.id}`);
                        }
                      }}
                    >
                      <div className="scene-header">
                        <div className="scene-actions">
                          <Tooltip text="Edit Scene">
                            <button
                              className="scene-action-btn"
                              onClick={(e) => { e.stopPropagation(); handleEditScene(scene); }}
                            >
                              <i className="fa-solid fa-pen"></i>
                            </button>
                          </Tooltip>
                          <Tooltip text="Delete Scene">
                            <button
                              className="scene-action-btn delete"
                              onClick={(e) => { e.stopPropagation(); /* handle delete when implemented */ }}
                            >
                              <i className="fa-solid fa-trash-can"></i>
                            </button>
                          </Tooltip>
                        </div>
                      </div>

                      <div className="scene-content">
                        <h4>{scene.title}</h4>
                        <p className="scene-description">
                          {scene.description || 'No description provided'}
                        </p>

                        <div className="scene-meta">
                          <div className="meta-item">
                            <span className="meta-icon"><i className="fa-solid fa-masks-theater"></i></span>
                            <span>0 shots</span>
                          </div>
                          <div className="meta-item">
                            <span className="meta-icon"><i className="fa-regular fa-calendar"></i></span>
                            <span>{formatDate(scene.created_at)}</span>
                          </div>
                        </div>
                      </div>

                      <div className="scene-footer">
                        <button
                          className="btn-secondary scene-view-btn"
                          onClick={(e) => { e.stopPropagation(); router.push(`/projects/${id}/scenes/${scene.id}`); }}
                        >
                          <i className="fa-regular fa-eye" style={{ marginRight: 8 }}></i> View Shots
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Danger Zone */}
          <div className="danger-zone">
            <div className="danger-zone-header">
              <h3><i className="fa-solid fa-triangle-exclamation" style={{ marginRight: 8 }}></i> Danger Zone</h3>
              <p>This action is irreversible. Deleting a project removes all its scenes, shots, and uploaded contents.</p>
            </div>
            <button className="btn-danger" onClick={() => setDangerOpen(!dangerOpen)}>
              <i className="fa-solid fa-trash" style={{ marginRight: 8 }}></i>
              {dangerOpen ? 'Hide Delete Options' : 'Delete Project'}
            </button>

            {dangerOpen && (
              <div className="danger-zone-body">
                <p>
                  Are you sure you want to delete this project? The data will be lost and cannot be reverted.
                </p>
                <p>
                  Please write the project title '{project?.title}' in the input below to confirm.
                </p>
                <input
                  type="text"
                  placeholder={project?.title}
                  value={deleteTitleInput}
                  onChange={(e) => setDeleteTitleInput(e.target.value)}
                  className="danger-input"
                />
                {deleteMsg && (
                  <div className={`message ${deleteMsg.includes('success') || deleteMsg.includes('Redirecting') ? 'success' : 'error'}`}>
                    {deleteMsg}
                  </div>
                )}
                <button
                  className="btn-danger"
                  disabled={deleting || deleteTitleInput.trim() !== (project?.title || '').trim()}
                  onClick={handleDeleteProject}
                >
                  {deleting ? 'Deleting…' : (<><i className="fa-solid fa-trash" style={{ marginRight: 8 }}></i> Permanently Delete Project</>)}
                </button>
              </div>
            )}
          </div>

        </div>        

        {/* Edit Project Modal */}
        <Modal 
          isOpen={showEditModal} 
          onClose={() => {
            setShowEditModal(false);
            setFormData({ title: '', description: '', image: null });
            setImagePreview(null);
            setMessage('');
          }}
          title="Edit Project"
        >
          <form className="project-form" onSubmit={handleUpdateProject}>
            <div className="form-group">
              <label>Project Title *</label>
              <input 
                type="text" 
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter project title..." 
                required
              />
            </div>
            
            <div className="form-group">
              <label>Description</label>
              <textarea 
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe your project..." 
                rows="4"
              ></textarea>
            </div>
            
            <div className="form-group">
              <label>Project Banner Image</label>
              <div className="image-upload-container">
                <input 
                  type="file" 
                  id="image-upload-edit"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="image-input"
                />
                <label htmlFor="image-upload-edit" className="image-upload-label">
                  {imagePreview ? (
                    <div className="image-preview">
                      <img src={imagePreview} alt="Preview" />
                      <div className="image-overlay">
                        <span>Click to change image</span>
                      </div>
                    </div>
                  ) : (
                    <div className="upload-placeholder">
                      <span className="upload-icon"><i className="fa-solid fa-camera"></i></span>
                      <span>Click to upload banner image</span>
                      <small>Current image will be kept if no new image is uploaded</small>
                    </div>
                  )}
                </label>
              </div>
            </div>

            {message && (
              <div className={`message ${message.includes('successfully') ? 'success' : 'error'}`}>
                {message}
              </div>
            )}
            
            <button type="submit" className="auth-button" disabled={submitting}>
              {submitting ? (
                <>
                  <div className="spinner"></div>
                  Updating Project...
                </>
              ) : (
                <>
                  <i className="fa-solid fa-pen" style={{ marginRight: 8 }}></i> Update Project
                </>
              )}
            </button>
          </form>
        </Modal>

        {/* Create Scene Modal */}
        <Modal
          isOpen={showSceneModal}
          onClose={() => {
            setShowSceneModal(false);
            setSceneFormData({ title: '', description: '' });
            setSceneMessage('');
            setEditingScene(null);
            setIsEditingScene(false);
          }}
          title={isEditingScene ? "Edit Scene" : "Create New Scene"}
        >
          <form className="project-form" onSubmit={handleCreateScene}>
            <div className="form-group">
              <label>Scene Title *</label>
              <input
                type="text"
                name="title"
                value={sceneFormData.title}
                onChange={handleSceneInputChange}
                placeholder="Enter scene title..."
                required
              />
            </div>

            <div className="form-group">
              <label>Description</label>
              <textarea
                name="description"
                value={sceneFormData.description}
                onChange={handleSceneInputChange}
                placeholder="Describe what happens in this scene..."
                rows="4"
              ></textarea>
            </div>

            {sceneMessage && (
              <div className={`message ${sceneMessage.includes('successfully') ? 'success' : 'error'}`}>
                {sceneMessage}
              </div>
            )}

            <button type="submit" className="auth-button" disabled={sceneSubmitting}>
              {sceneSubmitting ? (
                <>
                  <div className="spinner"></div>
                  {isEditingScene ? 'Updating Scene...' : 'Creating Scene...'}
                </>
              ) : (
                <>
                  {isEditingScene ? (<><i className="fa-solid fa-pen" style={{ marginRight: 8 }}></i> Update Scene</>) : (<><i className="fa-solid fa-clapperboard" style={{ marginRight: 8 }}></i> Create Scene</>)}
                </>
              )}
            </button>
          </form>
        </Modal>
      </div>
    </>
  );
}
