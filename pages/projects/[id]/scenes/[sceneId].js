import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../../../../components/Navbar';
import Modal from '../../../../components/Modal';
import Tooltip from '../../../../components/Tooltip';

export default function SceneShotsPage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState(null);
  const [scene, setScene] = useState(null);
  const [shots, setShots] = useState([]);
  const [showShotModal, setShowShotModal] = useState(false);
  const [shotFormData, setShotFormData] = useState({
    title: '',
    content: '',
    ai_prompt: ''
  });
  const [shotSubmitting, setShotSubmitting] = useState(false);
  const [shotMessage, setShotMessage] = useState('');
  const [editingShot, setEditingShot] = useState(null);
  const [isEditingShot, setIsEditingShot] = useState(false);
  const [copiedPromptId, setCopiedPromptId] = useState(null);
  const [lightbox, setLightbox] = useState(null); // { type: 'image'|'video', src: string }
  const audioRef = useRef({ currentId: null, audio: null });
  const router = useRouter();
  const { id: projectId, sceneId } = router.query;

  useEffect(() => {
    if (!projectId || !sceneId) return;

    const fetchData = async () => {
      try {
        // Fetch user data
        const userRes = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (userRes.ok) {
          const userData = await userRes.json();
          setUser(userData);
          
          // Fetch project details
          const projectRes = await fetch(`http://localhost:3001/api/projects/${projectId}`, {
            credentials: 'include'
          });
          if (projectRes.ok) {
            const projectData = await projectRes.json();
            setProject(projectData.project);
          }
          
          // Fetch scene details
          const sceneRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}`, {
            credentials: 'include'
          });
          if (sceneRes.ok) {
            const sceneData = await sceneRes.json();
            setScene(sceneData.scene);
            
            // Fetch shots for this scene
            const shotsRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots`, {
              credentials: 'include'
            });
            if (shotsRes.ok) {
              const shotsData = await shotsRes.json();
              const baseShots = shotsData.shots || [];
              setShots(baseShots);

              // Fallback: enrich with live content counts if missing/zero
              try {
                const enriched = await Promise.all(
                  baseShots.map(async (s) => {
                    const res = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${s.id}/content`, { credentials: 'include' });
                    if (res.ok) {
                      const data = await res.json();
                      const contents = data.contents || [];
                      const previews = contents.slice(0, 6); // keep it light
                      return { ...s, content_count: contents.length, contents_preview: previews };
                    }
                    return s;
                  })
                );
                setShots(enriched);
              } catch (e) {
                // Non-blocking; keep initial shots if counts fail
                console.warn('Failed to enrich shot content counts', e);
              }
            }
          } else {
            router.push(`/projects/${projectId}`);
          }
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId, sceneId, router]);

  const handleShotInputChange = (e) => {
    const { name, value } = e.target;
    setShotFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const toAssetUrl = (p) => {
    if (!p) return '';
    return /^https?:\/\//i.test(p) ? p : `http://localhost:3001${p}`;
  };

  const openPreview = (item) => {
    if (!item) return;
    if (item.content_type !== 'image' && item.content_type !== 'video') return;
    setLightbox({ type: item.content_type, src: toAssetUrl(item.file_path) });
  };

  const toggleAudio = (content) => {
    if (!content || content.content_type !== 'audio') return;
    const id = content.id;
    const ref = audioRef.current;
    // If clicking the same currently playing: stop
    if (ref.currentId === id && ref.audio) {
      try { ref.audio.pause(); ref.audio.currentTime = 0; } catch {}
      ref.currentId = null;
      ref.audio = null;
      // force re-render
      setShots((prev) => [...prev]);
      return;
    }
    // Stop any existing
    if (ref.audio) {
      try { ref.audio.pause(); } catch {}
    }
    const a = new Audio(toAssetUrl(content.file_path));
    ref.audio = a;
    ref.currentId = id;
    a.play().catch(() => {/* ignore */});
    a.onended = () => {
      ref.currentId = null;
      ref.audio = null;
      setShots((prev) => [...prev]);
    };
    setShots((prev) => [...prev]);
  };

  const handleEditShot = (shot) => {
    setEditingShot(shot);
    setIsEditingShot(true);
    setShotFormData({
      title: shot.title,
      content: shot.content || '',
      ai_prompt: shot.ai_prompt || ''
    });
    setShowShotModal(true);
  };

  const copyShotAiPrompt = async (text, id) => {
    const toCopy = text || '';
    if (!toCopy) return;
    try {
      if (navigator?.clipboard?.writeText) {
        await navigator.clipboard.writeText(toCopy);
      } else {
        const ta = document.createElement('textarea');
        ta.value = toCopy;
        ta.style.position = 'fixed';
        ta.style.top = '-1000px';
        document.body.appendChild(ta);
        ta.focus();
        ta.select();
        document.execCommand('copy');
        document.body.removeChild(ta);
      }
      setCopiedPromptId(id);
      setTimeout(() => setCopiedPromptId((curr) => (curr === id ? null : curr)), 1500);
    } catch (e) {
      console.warn('Failed to copy AI prompt', e);
    }
  };

  const handleCreateShot = async (e) => {
    e.preventDefault();
    
    if (!shotFormData.title.trim()) {
      setShotMessage('Shot title is required');
      return;
    }

    setShotSubmitting(true);
    setShotMessage('');

    try {
      const url = isEditingShot 
        ? `http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${editingShot.id}`
        : `http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots`;
      
      const method = isEditingShot ? 'PUT' : 'POST';

      const res = await fetch(url, {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          title: shotFormData.title.trim(),
          content: shotFormData.content.trim(),
          ai_prompt: shotFormData.ai_prompt.trim()
        })
      });

      const data = await res.json();
      
      if (res.ok) {
        setShotMessage(isEditingShot ? 'Shot updated successfully!' : 'Shot created successfully!');
        
        // Reset form
        setShotFormData({ title: '', content: '', ai_prompt: '' });
        setEditingShot(null);
        setIsEditingShot(false);
        
        // Refresh shots list
        const shotsRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots`, {
          credentials: 'include'
        });
        if (shotsRes.ok) {
          const shotsData = await shotsRes.json();
          setShots(shotsData.shots);
        }
        
        // Close modal after a short delay
        setTimeout(() => {
          setShowShotModal(false);
          setShotMessage('');
        }, 1500);
      } else {
        setShotMessage(data.message || `Failed to ${isEditingShot ? 'update' : 'create'} shot`);
      }
    } catch (error) {
      console.error(`Error ${isEditingShot ? 'updating' : 'creating'} shot:`, error);
      setShotMessage('Connection error. Please try again.');
    } finally {
      setShotSubmitting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  if (!project || !scene) {
    return (
      <>
        <Navbar />
        <div className="container" style={{ paddingTop: '100px' }}>
          <div className="error-state">
            <h2>Scene not found</h2>
            <button className="cta-primary" onClick={() => router.push(`/projects/${projectId}`)}>
              <i className="fa-solid fa-arrow-left" style={{ marginRight: 6 }}></i> Back to Project
            </button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container" style={{ paddingTop: '100px' }}>
        <div className="shots-container">
          {/* Scene Header */}
          <div className="shots-header">
            <div className="breadcrumb">
              <button className="breadcrumb-link" onClick={() => router.push('/projects')}>
                Projects
              </button>
              <span className="breadcrumb-separator">›</span>
              <button className="breadcrumb-link" onClick={() => router.push(`/projects/${projectId}`)}>
                {project.title}
              </button>
              <span className="breadcrumb-separator">›</span>
              <span className="breadcrumb-current">{scene.title}</span>
            </div>
            
            <div className="scene-actions">
              <button className="cta-primary" onClick={() => {
                setIsEditingShot(false);
                setEditingShot(null);
                setShotFormData({ title: '', content: '', ai_prompt: '' });
                setShotMessage('');
                setShowShotModal(true);
              }}>
                <i className="fa-solid fa-plus" style={{ marginRight: 8 }}></i> Create Shot
              </button>
            </div>
          </div>

          {/* Scene Info */}
          <div className="scene-info-card">
            <h1>{scene.title}</h1>
            <p className="scene-description">
              {scene.description || 'No description provided for this scene.'}
            </p>
            <div className="scene-meta">
              <span className="meta-badge">
                <i className="fa-solid fa-clapperboard" style={{ marginRight: 6 }}></i> {shots.length} shots
              </span>
              <span className="meta-badge">
                <i className="fa-regular fa-calendar" style={{ marginRight: 6 }}></i> Created {formatDate(scene.created_at)}
              </span>
            </div>
          </div>

          {/* Shots Section */}
          <div className="shots-section">
            <h2>Shots ({shots.length})</h2>
            
            {shots.length === 0 ? (
              <div className="empty-shots">
                <div className="empty-icon"><i className="fa-solid fa-camera"></i></div>
                <h3>No shots yet</h3>
                <p>Start creating your storyboard by adding shots to this scene!</p>
                <button className="cta-primary" onClick={() => {
                  setIsEditingShot(false);
                  setEditingShot(null);
                  setShotFormData({ title: '', content: '', ai_prompt: '' });
                  setShotMessage('');
                  setShowShotModal(true);
                }}>
                  <i className="fa-solid fa-camera" style={{ marginRight: 8 }}></i> Create First Shot
                </button>
              </div>
            ) : (
              <div className="shots-grid">
                {shots.map((shot, index) => (
                  <div
                    key={shot.id}
                    className="shot-card"
                    role="button"
                    tabIndex={0}
                    style={{ cursor: 'pointer' }}
                    onClick={() => router.push(`/projects/${projectId}/scenes/${sceneId}/shots/${shot.id}`)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        router.push(`/projects/${projectId}/scenes/${sceneId}/shots/${shot.id}`);
                      }
                    }}
                  >
                    <div className="shot-header">
                      <div className="shot-actions">
                        <Tooltip text="Edit Shot">
                          <button
                            className="shot-action-btn"
                            onClick={(e) => { e.stopPropagation(); handleEditShot(shot); }}
                          >
                            <i className="fa-solid fa-pen"></i>
                          </button>
                        </Tooltip>
                        <Tooltip text="Delete Shot">
                          <button
                            className="shot-action-btn delete"
                            onClick={(e) => { e.stopPropagation(); /* handle delete when implemented */ }}
                          >
                            <i className="fa-regular fa-trash-can"></i>
                          </button>
                        </Tooltip>
                      </div>
                    </div>
                    
                    <div className="shot-content">
                      <h4>{shot.title}</h4>
                      <p className="shot-description">
                        {shot.content || 'No content provided'}
                      </p>
                      
                      {shot.ai_prompt && (
                        <div className="ai-prompt-section">
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                            <label>AI Prompt:</label>
                            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                              <button
                                className="cta-secondary"
                                onClick={(e) => { e.stopPropagation(); copyShotAiPrompt(shot.ai_prompt, shot.id); }}
                                aria-label="Copy AI prompt"
                                style={{ padding: `6px 10px`, height: `32px`, fontSize: `12px`, display: 'inline-flex', alignItems: 'center', justifyContent: 'center' }}
                              >
                                {copiedPromptId === shot.id ? 'Copied' : (<><i className="fa-regular fa-copy" style={{ marginRight: 0 }}></i></>)}
                              </button>
                            </div>
                          </div>
                          <p className="ai-prompt">{shot.ai_prompt}</p>
                        </div>
                      )}
                      
                      {/* Thumbnails row */}
                      {Array.isArray(shot.contents_preview) && shot.contents_preview.length > 0 && (
                        <div className="shot-thumbs" onClick={(e) => e.stopPropagation()}>
                          {shot.contents_preview.map((c) => {
                            if (c.content_type === 'image') {
                              return (
                                <div key={c.id} className="thumb media" role="button" tabIndex={0}
                                  onClick={() => openPreview(c)}
                                  onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); openPreview(c); } }}
                                >
                                  <img src={toAssetUrl(c.file_path)} alt={c.notes || 'Shot image'} />
                                  <div className="thumb-overlay"><i className="fa-solid fa-magnifying-glass-plus"></i></div>
                                </div>
                              );
                            }
                            if (c.content_type === 'video') {
                              return (
                                <div key={c.id} className="thumb media" role="button" tabIndex={0}
                                  onClick={() => openPreview(c)}
                                  onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); openPreview(c); } }}
                                >
                                  <div className="video-thumb">
                                    <video muted>
                                      <source src={toAssetUrl(c.file_path)} />
                                    </video>
                                    <div className="thumb-overlay"><i className="fa-regular fa-circle-play"></i></div>
                                  </div>
                                </div>
                              );
                            }
                            if (c.content_type === 'audio') {
                              const isPlaying = audioRef.current.currentId === c.id;
                              return (
                                <button
                                  key={c.id}
                                  className="thumb audio-btn"
                                  style={{ marginTop: '0px' }}
                                  onClick={() => toggleAudio(c)}
                                  aria-label={isPlaying ? 'Stop audio' : 'Play audio'}
                                >
                                  <i className={isPlaying ? 'fa-solid fa-stop' : 'fa-solid fa-circle-play'}></i>
                                </button>
                              );
                            }
                            return null;
                          })}
                        </div>
                      )}

                      <div className="shot-meta">
                        <div className="meta-item">
                          <span className="meta-icon"><i className="fa-regular fa-calendar"></i></span>
                          <span>{formatDate(shot.created_at)}</span>
                        </div>
                      </div>

                      <button
                        className="scene-view-btn"
                        onClick={(e) => { e.stopPropagation(); router.push(`/projects/${projectId}/scenes/${sceneId}/shots/${shot.id}`); }}
                      >
                        <i className="fa-regular fa-folder-open" style={{ marginRight: 8 }}></i> View Shot Contents ({shot.content_count ?? 0})
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Lightbox Modal for image/video previews */}
        <Modal
          isOpen={!!lightbox}
          onClose={() => setLightbox(null)}
          title={lightbox?.type === 'video' ? 'Preview Video' : 'Preview Image'}
          wide
        >
          {lightbox?.type === 'image' ? (
            <img src={lightbox.src} alt="Preview" style={{ width: '100%', height: 'auto', borderRadius: 12 }} />
          ) : lightbox?.type === 'video' ? (
            <video controls style={{ width: '100%', borderRadius: 12 }}>
              <source src={lightbox.src} />
              Your browser does not support the video tag.
            </video>
          ) : null}
        </Modal>

        {/* Create/Edit Shot Modal */}
        <Modal 
          isOpen={showShotModal} 
          onClose={() => {
            setShowShotModal(false);
            setShotFormData({ title: '', content: '', ai_prompt: '' });
            setShotMessage('');
            setEditingShot(null);
            setIsEditingShot(false);
          }}
          title={isEditingShot ? "Edit Shot" : "Create New Shot"}
        >
          <form className="project-form" onSubmit={handleCreateShot}>
            <div className="form-group">
              <label>Shot Title *</label>
              <input 
                type="text" 
                name="title"
                value={shotFormData.title}
                onChange={handleShotInputChange}
                placeholder="Enter shot title..." 
                required
              />
            </div>
            
            <div className="form-group">
              <label>Shot Content</label>
              <textarea 
                name="content"
                value={shotFormData.content}
                onChange={handleShotInputChange}
                placeholder="Describe what happens in this shot..." 
                rows="4"
              ></textarea>
            </div>
            
            <div className="form-group">
              <label>AI Prompt</label>
              <textarea 
                name="ai_prompt"
                value={shotFormData.ai_prompt}
                onChange={handleShotInputChange}
                placeholder="Enter AI prompt for generating content..." 
                rows="3"
              ></textarea>
            </div>

            {shotMessage && (
              <div className={`message ${shotMessage.includes('successfully') ? 'success' : 'error'}`}>
                {shotMessage}
              </div>
            )}
            
            <button type="submit" className="auth-button" disabled={shotSubmitting}>
              {shotSubmitting ? (
                <>
                  <div className="spinner"></div>
                  {isEditingShot ? 'Updating Shot...' : 'Creating Shot...'}
                </>
              ) : (
                <>
                  {isEditingShot ? (<><i className="fa-solid fa-pen" style={{ marginRight: 8 }}></i> Update Shot</>) : (<><i className="fa-solid fa-camera" style={{ marginRight: 8 }}></i> Create Shot</>)}
                </>
              )}
            </button>
          </form>
        </Modal>
      </div>
    </>
  );
}
