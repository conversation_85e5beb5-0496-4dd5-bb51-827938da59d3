import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../../../../components/Navbar';
import Modal from '../../../../components/Modal';
import Tooltip from '../../../../components/Tooltip';

export default function SceneShotsPage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState(null);
  const [scene, setScene] = useState(null);
  const [shots, setShots] = useState([]);
  const [showShotModal, setShowShotModal] = useState(false);
  const [shotFormData, setShotFormData] = useState({
    title: '',
    content: '',
    ai_prompt: ''
  });
  const [shotSubmitting, setShotSubmitting] = useState(false);
  const [shotMessage, setShotMessage] = useState('');
  const [editingShot, setEditingShot] = useState(null);
  const [isEditingShot, setIsEditingShot] = useState(false);
  const router = useRouter();
  const { id: projectId, sceneId } = router.query;

  useEffect(() => {
    if (!projectId || !sceneId) return;

    const fetchData = async () => {
      try {
        // Fetch user data
        const userRes = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (userRes.ok) {
          const userData = await userRes.json();
          setUser(userData);
          
          // Fetch project details
          const projectRes = await fetch(`http://localhost:3001/api/projects/${projectId}`, {
            credentials: 'include'
          });
          if (projectRes.ok) {
            const projectData = await projectRes.json();
            setProject(projectData.project);
          }
          
          // Fetch scene details
          const sceneRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}`, {
            credentials: 'include'
          });
          if (sceneRes.ok) {
            const sceneData = await sceneRes.json();
            setScene(sceneData.scene);
            
            // Fetch shots for this scene
            const shotsRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots`, {
              credentials: 'include'
            });
            if (shotsRes.ok) {
              const shotsData = await shotsRes.json();
              setShots(shotsData.shots);
            }
          } else {
            router.push(`/projects/${projectId}`);
          }
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId, sceneId, router]);

  const handleShotInputChange = (e) => {
    const { name, value } = e.target;
    setShotFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEditShot = (shot) => {
    setEditingShot(shot);
    setIsEditingShot(true);
    setShotFormData({
      title: shot.title,
      content: shot.content || '',
      ai_prompt: shot.ai_prompt || ''
    });
    setShowShotModal(true);
  };

  const handleCreateShot = async (e) => {
    e.preventDefault();
    
    if (!shotFormData.title.trim()) {
      setShotMessage('Shot title is required');
      return;
    }

    setShotSubmitting(true);
    setShotMessage('');

    try {
      const url = isEditingShot 
        ? `http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${editingShot.id}`
        : `http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots`;
      
      const method = isEditingShot ? 'PUT' : 'POST';

      const res = await fetch(url, {
        method: method,
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          title: shotFormData.title.trim(),
          content: shotFormData.content.trim(),
          ai_prompt: shotFormData.ai_prompt.trim()
        })
      });

      const data = await res.json();
      
      if (res.ok) {
        setShotMessage(isEditingShot ? 'Shot updated successfully!' : 'Shot created successfully!');
        
        // Reset form
        setShotFormData({ title: '', content: '', ai_prompt: '' });
        setEditingShot(null);
        setIsEditingShot(false);
        
        // Refresh shots list
        const shotsRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots`, {
          credentials: 'include'
        });
        if (shotsRes.ok) {
          const shotsData = await shotsRes.json();
          setShots(shotsData.shots);
        }
        
        // Close modal after a short delay
        setTimeout(() => {
          setShowShotModal(false);
          setShotMessage('');
        }, 1500);
      } else {
        setShotMessage(data.message || `Failed to ${isEditingShot ? 'update' : 'create'} shot`);
      }
    } catch (error) {
      console.error(`Error ${isEditingShot ? 'updating' : 'creating'} shot:`, error);
      setShotMessage('Connection error. Please try again.');
    } finally {
      setShotSubmitting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  if (!project || !scene) {
    return (
      <>
        <Navbar />
        <div className="container" style={{ paddingTop: '100px' }}>
          <div className="error-state">
            <h2>Scene not found</h2>
            <button className="cta-primary" onClick={() => router.push(`/projects/${projectId}`)}>
              ← Back to Project
            </button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container" style={{ paddingTop: '100px' }}>
        <div className="shots-container">
          {/* Scene Header */}
          <div className="shots-header">
            <div className="breadcrumb">
              <button className="breadcrumb-link" onClick={() => router.push('/projects')}>
                Projects
              </button>
              <span className="breadcrumb-separator">›</span>
              <button className="breadcrumb-link" onClick={() => router.push(`/projects/${projectId}`)}>
                {project.title}
              </button>
              <span className="breadcrumb-separator">›</span>
              <span className="breadcrumb-current">{scene.title}</span>
            </div>
            
            <div className="scene-actions">
              <button className="cta-primary" onClick={() => {
                setIsEditingShot(false);
                setEditingShot(null);
                setShotFormData({ title: '', content: '', ai_prompt: '' });
                setShotMessage('');
                setShowShotModal(true);
              }}>
                ➕ Create Shot
              </button>
            </div>
          </div>

          {/* Scene Info */}
          <div className="scene-info-card">
            <h1>{scene.title}</h1>
            <p className="scene-description">
              {scene.description || 'No description provided for this scene.'}
            </p>
            <div className="scene-meta">
              <span className="meta-badge">
                🎬 {shots.length} shots
              </span>
              <span className="meta-badge">
                📅 Created {formatDate(scene.created_at)}
              </span>
            </div>
          </div>

          {/* Shots Section */}
          <div className="shots-section">
            <h2>Shots ({shots.length})</h2>
            
            {shots.length === 0 ? (
              <div className="empty-shots">
                <div className="empty-icon">📸</div>
                <h3>No shots yet</h3>
                <p>Start creating your storyboard by adding shots to this scene!</p>
                <button className="cta-primary" onClick={() => {
                  setIsEditingShot(false);
                  setEditingShot(null);
                  setShotFormData({ title: '', content: '', ai_prompt: '' });
                  setShotMessage('');
                  setShowShotModal(true);
                }}>
                  📸 Create First Shot
                </button>
              </div>
            ) : (
              <div className="shots-grid">
                {shots.map((shot, index) => (
                  <div key={shot.id} className="shot-card">
                    <div className="shot-header">
                      <div className="shot-number">Shot {shot.ordering || index + 1}</div>
                      <div className="shot-actions">
                        <Tooltip text="Edit Shot">
                          <button className="shot-action-btn" onClick={() => handleEditShot(shot)}>✏️</button>
                        </Tooltip>
                        <Tooltip text="Delete Shot">
                          <button className="shot-action-btn delete">🗑️</button>
                        </Tooltip>
                      </div>
                    </div>
                    
                    <div className="shot-content">
                      <h4>{shot.title}</h4>
                      <p className="shot-description">
                        {shot.content || 'No content provided'}
                      </p>
                      
                      {shot.ai_prompt && (
                        <div className="ai-prompt-section">
                          <label>AI Prompt:</label>
                          <p className="ai-prompt">{shot.ai_prompt}</p>
                        </div>
                      )}
                      
                      <div className="shot-meta">
                        <div className="meta-item">
                          <span className="meta-icon">📅</span>
                          <span>{formatDate(shot.created_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Create/Edit Shot Modal */}
        <Modal 
          isOpen={showShotModal} 
          onClose={() => {
            setShowShotModal(false);
            setShotFormData({ title: '', content: '', ai_prompt: '' });
            setShotMessage('');
            setEditingShot(null);
            setIsEditingShot(false);
          }}
          title={isEditingShot ? "Edit Shot" : "Create New Shot"}
        >
          <form className="project-form" onSubmit={handleCreateShot}>
            <div className="form-group">
              <label>Shot Title *</label>
              <input 
                type="text" 
                name="title"
                value={shotFormData.title}
                onChange={handleShotInputChange}
                placeholder="Enter shot title..." 
                required
              />
            </div>
            
            <div className="form-group">
              <label>Shot Content</label>
              <textarea 
                name="content"
                value={shotFormData.content}
                onChange={handleShotInputChange}
                placeholder="Describe what happens in this shot..." 
                rows="4"
              ></textarea>
            </div>
            
            <div className="form-group">
              <label>AI Prompt</label>
              <textarea 
                name="ai_prompt"
                value={shotFormData.ai_prompt}
                onChange={handleShotInputChange}
                placeholder="Enter AI prompt for generating content..." 
                rows="3"
              ></textarea>
            </div>

            {shotMessage && (
              <div className={`message ${shotMessage.includes('successfully') ? 'success' : 'error'}`}>
                {shotMessage}
              </div>
            )}
            
            <button type="submit" className="auth-button" disabled={shotSubmitting}>
              {shotSubmitting ? (
                <>
                  <div className="spinner"></div>
                  {isEditingShot ? 'Updating Shot...' : 'Creating Shot...'}
                </>
              ) : (
                <>
                  {isEditingShot ? '✏️ Update Shot' : '📸 Create Shot'}
                </>
              )}
            </button>
          </form>
        </Modal>
      </div>
    </>
  );
}
