import { useEffect, useState, useMemo, useRef } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../../../../../../components/Navbar';
import Modal from '../../../../../../components/Modal';

export default function ShotContentsPage() {
  const router = useRouter();
  const { id: projectId, sceneId, shotId } = router.query;

  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [project, setProject] = useState(null);
  const [scene, setScene] = useState(null);
  const [shot, setShot] = useState(null);
  const [contents, setContents] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadMsg, setUploadMsg] = useState('');
  const [file, setFile] = useState(null);
  const [contentType, setContentType] = useState('auto');
  const [notesInput, setNotesInput] = useState('');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [notesEdits, setNotesEdits] = useState({});
  const [notesStatus, setNotesStatus] = useState({}); // { [contentId]: { msg, type: 'success'|'error'|'info' } }
  const [copiedPrompt, setCopiedPrompt] = useState(false);
  const [lightbox, setLightbox] = useState(null); // { type: 'image'|'video', src: string }
  // Drag & drop upload UI state
  const [dragActive, setDragActive] = useState(false);
  const [uploadQueue, setUploadQueue] = useState([]); // [{ id, file, preview, kind, progress, status: 'queued'|'uploading'|'done'|'error' }]
  const [trayVisible, setTrayVisible] = useState(false);
  const trayHideTimer = useRef(null);
  // Library search state
  const [showLibraryModal, setShowLibraryModal] = useState(false);
  const [libraryQuery, setLibraryQuery] = useState('');
  const [libraryItems, setLibraryItems] = useState([]);
  const [libraryLoading, setLibraryLoading] = useState(false);

  useEffect(() => {
    if (!projectId || !sceneId || !shotId) return;

    const fetchAll = async () => {
      try {
        // Auth check
        const userRes = await fetch('http://localhost:3001/api/home', { credentials: 'include' });
        if (!userRes.ok) { router.push('/login'); return; }
        const userData = await userRes.json();
        setUser(userData);

        // Project and scene
        const [projectRes, sceneRes] = await Promise.all([
          fetch(`http://localhost:3001/api/projects/${projectId}`, { credentials: 'include' }),
          fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}`, { credentials: 'include' })
        ]);
        if (projectRes.ok) {
          const p = await projectRes.json();
          setProject(p.project);
        }
        if (sceneRes.ok) {
          const s = await sceneRes.json();
          setScene(s.scene);
        }

        // Get the shot details from the scene's shots list (no single-shot endpoint)
        const shotsRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots`, { credentials: 'include' });
        if (shotsRes.ok) {
          const shotsData = await shotsRes.json();
          const found = (shotsData.shots || []).find(x => String(x.id) === String(shotId));
          if (found) setShot(found);
        }

        // Fetch contents
        const contentsRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${shotId}/content`, { credentials: 'include' });
        if (contentsRes.ok) {
          const contentData = await contentsRes.json();
          setContents(contentData.contents || []);
        }
      } catch (err) {
        console.error('Error loading shot contents:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchAll();
  }, [projectId, sceneId, shotId, router]);

  // Load library index on demand
  useEffect(() => {
    let aborted = false;
    const q = (libraryQuery || '').trim();
    if (!showLibraryModal) return;
    if (q.length < 3) { setLibraryItems([]); return; }
    (async () => {
      try {
        setLibraryLoading(true);
        const res = await fetch(`http://localhost:3001/api/library/audio?q=${encodeURIComponent(q)}&limit=200`);
        if (res.ok) {
          const data = await res.json();
          if (!aborted) setLibraryItems(Array.isArray(data.items) ? data.items : []);
        } else {
          if (!aborted) setLibraryItems([]);
        }
      } catch (e) {
        if (!aborted) setLibraryItems([]);
      } finally {
        if (!aborted) setLibraryLoading(false);
      }
    })();
    return () => { aborted = true; };
  }, [showLibraryModal, libraryQuery]);

  const backToScene = () => router.push(`/projects/${projectId}/scenes/${sceneId}`);

  const refreshContents = async () => {
    const contentsRes = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${shotId}/content`, { credentials: 'include' });
    if (contentsRes.ok) {
      const contentData = await contentsRes.json();
      setContents(contentData.contents || []);
    }
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    if (!file) { setUploadMsg('Please choose a file to upload'); return false; }
    setUploading(true);
    setUploadMsg('');
    try {
      const form = new FormData();
      form.append('file', file);
      if (contentType !== 'auto') form.append('content_type', contentType);
      if (notesInput) form.append('notes', notesInput);

      const res = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${shotId}/content`, {
        method: 'POST',
        credentials: 'include',
        body: form
      });
      const data = await res.json();
      if (res.ok) {
        setUploadMsg('Uploaded successfully');
        setFile(null);
        setContentType('auto');
        setNotesInput('');
        await refreshContents();
        setTimeout(() => setUploadMsg(''), 1500);
        return true;
      } else {
        setUploadMsg(data.message || 'Upload failed');
        return false;
      }
    } catch (err) {
      console.error('Upload error:', err);
      setUploadMsg('Connection error. Please try again.');
      return false;
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async (contentId) => {
    if (!confirm('Delete this content item?')) return;
    try {
      const res = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${shotId}/content/${contentId}`, {
        method: 'DELETE',
        credentials: 'include'
      });
      const data = await res.json();
      if (res.ok) {
        await refreshContents();
      } else {
        alert(data.message || 'Failed to delete content');
      }
    } catch (err) {
      console.error('Delete error:', err);
      alert('Connection error while deleting');
    }
  };

  const uploadsSummary = useMemo(() => {
    const total = uploadQueue.length;
    const uploaded = uploadQueue.filter(u => u.status === 'done').length;
    const uploading = uploadQueue.filter(u => u.status === 'uploading').length;
    return { total, uploaded, uploading };
  }, [uploadQueue]);

  const enqueueFiles = (files) => {
    if (!files || files.length === 0) return;
    const items = Array.from(files).map((file, idx) => {
      const id = `${Date.now()}_${idx}_${file.name}`;
      const kind = file.type.startsWith('image/') ? 'image' : file.type.startsWith('video/') ? 'video' : file.type.startsWith('audio/') ? 'audio' : 'other';
      const preview = kind === 'image' || kind === 'video' ? URL.createObjectURL(file) : null;
      return { id, file, preview, kind, progress: 0, status: 'queued' };
    });
    setUploadQueue(prev => [...prev, ...items]);
    setTrayVisible(true);
    // cancel any pending hide while new uploads are queued
    if (trayHideTimer.current) { clearTimeout(trayHideTimer.current); trayHideTimer.current = null; }
  };

  // Drive the queue: upload one at a time when available
  useEffect(() => {
    if (!projectId || !sceneId || !shotId) return;
    const isUploading = uploadQueue.some(u => u.status === 'uploading');
    if (isUploading) return;
    const next = uploadQueue.find(u => u.status === 'queued');
    if (next) {
      uploadSingle(next);
    }
  }, [uploadQueue, projectId, sceneId, shotId]);

  // When all uploads complete, refresh contents and revoke previews
  useEffect(() => {
    const hasAny = uploadQueue.length > 0;
    const anyActive = uploadQueue.some(u => u.status === 'queued' || u.status === 'uploading');
    if (hasAny && !anyActive) {
      refreshContents();
      // Revoke object URLs to free memory
      uploadQueue.forEach(u => { if (u.preview) { try { URL.revokeObjectURL(u.preview); } catch {} } });
      // auto-hide tray shortly after completion
      if (!trayHideTimer.current) {
        trayHideTimer.current = setTimeout(() => {
          setTrayVisible(false);
          setUploadQueue([]);
          trayHideTimer.current = null;
        }, 2500);
      }
    }
  }, [uploadQueue]);

  useEffect(() => () => { if (trayHideTimer.current) clearTimeout(trayHideTimer.current); }, []);

  const uploadSingle = (item) => new Promise((resolve) => {
    setUploadQueue(prev => prev.map(u => u.id === item.id ? { ...u, status: 'uploading', progress: 0 } : u));
    const form = new FormData();
    form.append('file', item.file);
    // Only set explicit content_type for known kinds; otherwise let server auto-detect
    if (item.kind === 'image' || item.kind === 'video' || item.kind === 'audio') {
      form.append('content_type', item.kind);
    }
    const xhr = new XMLHttpRequest();
    xhr.open('POST', `http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${shotId}/content`);
    xhr.withCredentials = true;
    xhr.upload.onprogress = (e) => {
      if (e.lengthComputable) {
        const pct = Math.round((e.loaded / e.total) * 100);
        setUploadQueue(prev => prev.map(u => u.id === item.id ? { ...u, progress: pct } : u));
      }
    };
    xhr.onload = () => {
      const ok = xhr.status >= 200 && xhr.status < 300;
      setUploadQueue(prev => prev.map(u => u.id === item.id ? { ...u, status: ok ? 'done' : 'error', progress: 100 } : u));
      resolve();
    };
    xhr.onerror = () => {
      setUploadQueue(prev => prev.map(u => u.id === item.id ? { ...u, status: 'error' } : u));
      resolve();
    };
    xhr.send(form);
  });

  const handleSaveNotes = async (contentId) => {
    const newNotes = notesEdits[contentId] ?? '';
    try {
      setNotesStatus(prev => ({ ...prev, [contentId]: { msg: 'Saving…', type: 'info' } }));
      const res = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${shotId}/content/${contentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ notes: newNotes })
      });
      const data = await res.json();
      if (res.ok) {
        await refreshContents();
        setNotesStatus(prev => ({ ...prev, [contentId]: { msg: 'Note successfully added.', type: 'success' } }));
        setTimeout(() => setNotesStatus(prev => ({ ...prev, [contentId]: { ...prev[contentId], msg: '' } })), 1800);
      } else {
        setNotesStatus(prev => ({ ...prev, [contentId]: { msg: data.message || 'Failed to update notes', type: 'error' } }));
      }
    } catch (err) {
      console.error('Notes update error:', err);
      setNotesStatus(prev => ({ ...prev, [contentId]: { msg: 'Connection error while updating notes', type: 'error' } }));
    }
  };

  const toAssetUrl = (p) => {
    if (!p) return '';
    return /^https?:\/\//i.test(p) ? p : `http://localhost:3001${p}`;
  };

  const renderContent = (item) => {
    if (item.content_type === 'image') {
      return (
        <img src={toAssetUrl(item.file_path)} alt={item.notes || 'Shot image'} style={{ width: '100%', borderRadius: '10px' }} />
      );
    }
    if (item.content_type === 'audio') {
      return (
        <audio controls style={{ width: '100%' }}>
          <source src={toAssetUrl(item.file_path)} />
          Your browser does not support the audio element.
        </audio>
      );
    }
    if (item.content_type === 'video') {
      return (
        <video controls style={{ width: '100%', borderRadius: '10px' }}>
          <source src={toAssetUrl(item.file_path)} />
          Your browser does not support the video tag.
        </video>
      );
    }
    return <div>Unsupported content</div>;
  };

  const openPreview = (item) => {
    if (!item) return;
    const type = item.content_type;
    if (type !== 'image' && type !== 'video') return;
    setLightbox({ type, src: toAssetUrl(item.file_path) });
  };

  const copyAiPrompt = async () => {
    const text = shot?.ai_prompt || '';
    if (!text) return;
    try {
      if (navigator?.clipboard?.writeText) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback for older browsers
        const ta = document.createElement('textarea');
        ta.value = text;
        ta.style.position = 'fixed';
        ta.style.top = '-1000px';
        document.body.appendChild(ta);
        ta.focus();
        ta.select();
        document.execCommand('copy');
        document.body.removeChild(ta);
      }
      setCopiedPrompt(true);
      setTimeout(() => setCopiedPrompt(false), 1500);
    } catch (e) {
      console.warn('Failed to copy AI prompt', e);
    }
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container"><div className="spinner" /></div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div
        className={`container drop-container ${dragActive ? 'is-dragging' : ''}`}
        style={{ paddingTop: '100px' }}
        onDragEnter={(e) => { e.preventDefault(); e.stopPropagation(); setDragActive(true); }}
        onDragOver={(e) => { e.preventDefault(); e.stopPropagation(); setDragActive(true); }}
        onDragLeave={(e) => { e.preventDefault(); e.stopPropagation(); setDragActive(false); }}
        onDrop={(e) => {
          e.preventDefault(); e.stopPropagation(); setDragActive(false);
          const { files } = e.dataTransfer || {};
          enqueueFiles(files);
        }}
      >
        <div className="shots-container">
          <div className="shots-header">
            <div className="breadcrumb">
              <button className="breadcrumb-link" onClick={() => router.push('/projects')}>Projects</button>
              <span className="breadcrumb-separator">›</span>
              <button className="breadcrumb-link" onClick={() => router.push(`/projects/${projectId}`)}>{project?.title || 'Project'}</button>
              <span className="breadcrumb-separator">›</span>
              <button className="breadcrumb-link" onClick={backToScene}>{scene?.title || 'Scene'}</button>
              <span className="breadcrumb-separator">›</span>
              <span className="breadcrumb-current">Shot Contents</span>
            </div>
            <div className="scene-actions">
              <button className="cta-secondary" onClick={backToScene}><i className="fa-solid fa-arrow-left" style={{ marginRight: 6 }}></i> Back to Scene</button>
            </div>
          </div>

          <div className="scene-info-card">
            <h1>{shot?.title || `Shot #${shotId}`}</h1>
            <h3 className="info-subtitle">Description</h3>
            <p className="scene-description">{shot?.content || 'No description for this shot.'}</p>

            <div className="ai-prompt-section" style={{ marginTop: 10 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 }}>
                <label>AI Prompt:</label>
                {shot?.ai_prompt ? (
                  <button
                    className="cta-secondary"
                    onClick={copyAiPrompt}
                    disabled={copiedPrompt}
                    aria-label="Copy AI prompt"
                    style={{ padding: '6px 10px', fontSize: 12, height: `32px` }}
                  >
                    {copiedPrompt ? 'Copied' : (<><i className="fa-regular fa-copy" style={{ marginRight: 0 }}></i></>)}
                  </button>
                ) : null}
              </div>
              <p className="ai-prompt">{shot?.ai_prompt || 'No AI prompt provided.'}</p>
            </div>

            <div className="scene-meta">
              <span className="meta-badge"><i className="fa-solid fa-box-archive" style={{ marginRight: 6 }}></i> {contents.length} content item{contents.length === 1 ? '' : 's'}</span>
            </div>
          </div>

          {/* Upload form */}
          {/* Upload button moved to Contents header (no extra card to save space) */}

          <div className="shots-section">
            <div className="section-header" style={{ gap: 10, alignItems: 'center' }}>
              <h2 style={{ margin: 0 }}>Contents ({contents.length})</h2>
              <div style={{ marginLeft: 'auto', display: 'flex', gap: 10 }}>
                <button className="cta-primary" onClick={() => setShowUploadModal(true)}>
                  <i className="fa-solid fa-upload" style={{ marginRight: 8 }}></i> Add Content
                </button>
                <button className="cta-secondary" onClick={() => setShowLibraryModal(true)}>
                  <i className="fa-solid fa-music" style={{ marginRight: 8 }}></i> Add Content from Library
                </button>
              </div>
            </div>
            {contents.length === 0 ? (
              <div className="empty-shots">
                <div className="empty-icon"><i className="fa-solid fa-box-archive"></i></div>
                <h3>No content yet</h3>
                <p>This shot has no media content. Try uploading from the editor once available.</p>
              </div>
            ) : (
              <div className="masonry-grid">
                {contents.map(item => (
                  <div key={item.id} className="masonry-item">
                    <div className="scene-card" style={{ display: 'block' }}>
                      <div style={{ padding: 20 }}>
                        <div className="meta-badge" style={{ marginBottom: 10 }}>
                        {item.content_type.toUpperCase()}
                        </div>
                        {item.content_type === 'image' ? (
                          <div className="content-media">
                            <div
                              className="media-click"
                              role="button"
                              tabIndex={0}
                              onClick={() => openPreview(item)}
                              onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); openPreview(item); } }}
                            >
                              <img src={toAssetUrl(item.file_path)} alt={item.notes || 'Shot image'} />
                              <div className="media-overlay"><i className="fa-solid fa-magnifying-glass-plus"></i> View</div>
                            </div>
                          </div>
                        ) : item.content_type === 'video' ? (
                          <div className="content-media">
                            <div
                              className="media-click"
                              role="button"
                              tabIndex={0}
                              onClick={() => openPreview(item)}
                              onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); openPreview(item); } }}
                            >
                              <video muted>
                                <source src={toAssetUrl(item.file_path)} />
                              </video>
                              <div className="media-overlay"><i className="fa-regular fa-circle-play"></i> Play</div>
                            </div>
                          </div>
                        ) : item.content_type === 'audio' ? (
                          <div className="content-media audio">
                            <audio controls style={{ width: '100%' }}>
                              <source src={toAssetUrl(item.file_path)} />
                              Your browser does not support the audio element.
                            </audio>
                          </div>
                        ) : (
                          renderContent(item)
                        )}
                        <div className="content-actions" style={{ marginTop: 12 }}>
                        <input
                          type="text"
                          placeholder="Add notes…"
                          value={notesEdits[item.id] ?? (item.notes || '')}
                          onChange={(e) => setNotesEdits(prev => ({ ...prev, [item.id]: e.target.value }))}
                          style={{ width: '100%' }}
                        />
                        <div style={{ display: 'flex', gap: 10, marginTop: 10, alignItems: 'center' }}>
                          <button
                            className="cta-secondary"
                            onClick={() => handleSaveNotes(item.id)}
                            disabled={notesStatus[item.id]?.type === 'info'}
                          >
                          {notesStatus[item.id]?.type === 'info' ? 'Saving…' : (<><i className="fa-solid fa-floppy-disk" style={{ marginRight: 6 }}></i> Save Notes</>)}
                          </button>
                          <button className="cta-secondary" onClick={() => handleDelete(item.id)} style={{ color: '#ff6b6b', borderColor: 'rgba(255,107,107,0.4)' }}>
                            <i className="fa-solid fa-trash-can" style={{ marginRight: 6 }}></i> Delete
                          </button>
                        </div>
                        {notesStatus[item.id]?.msg ? (
                          <div className={`message ${notesStatus[item.id]?.type === 'success' ? 'success' : notesStatus[item.id]?.type === 'error' ? 'error' : ''}`} style={{ marginTop: 10 }}>
                            {notesStatus[item.id]?.msg}
                          </div>
                        ) : null}
                      </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        {/* Add/Upload Content Modal */}
        <Modal
          isOpen={showUploadModal}
          onClose={() => {
            setShowUploadModal(false);
            setFile(null);
            setContentType('auto');
            setNotesInput('');
            setUploadMsg('');
          }}
          title="Add Shot Content"
        >
          <form className="project-form" onSubmit={async (e) => {
            const ok = await handleUpload(e);
            if (ok) setShowUploadModal(false);
          }}>
            <div className="form-group">
              <label>File *</label>
              <input type="file" onChange={(e) => setFile(e.target.files?.[0] || null)} />
            </div>
            <div className="form-group">
              <label>Content Type</label>
              <select value={contentType} onChange={(e) => setContentType(e.target.value)}>
                <option value="auto">Auto-detect</option>
                <option value="image">Image</option>
                <option value="audio">Audio</option>
                <option value="video">Video</option>
              </select>
            </div>
            <div className="form-group">
              <label>Notes</label>
              <input type="text" placeholder="Optional notes" value={notesInput} onChange={(e) => setNotesInput(e.target.value)} />
            </div>
            {uploadMsg && (
              <div className={`message ${uploadMsg.includes('success') ? 'success' : 'error'}`}>{uploadMsg}</div>
            )}
            <button className="auth-button" type="submit" disabled={uploading}>
              {uploading ? 'Uploading…' : (<><i className="fa-solid fa-upload" style={{ marginRight: 8 }}></i> Upload Content</>)}
            </button>
          </form>
        </Modal>
        {/* Library Modal */}
        <Modal
          isOpen={showLibraryModal}
          onClose={() => { setShowLibraryModal(false); setLibraryQuery(''); }}
          title="Add From Audio Library"
          wide
          modalClassName="modal-audio-library"
        >
          <div className="project-form" style={{ marginBottom: 12 }}>
            <div className="form-group">
              <input
                type="text"
                placeholder="Search"
                value={libraryQuery}
                onChange={(e) => setLibraryQuery(e.target.value)}
              />
            </div>
          </div>
          <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
            {(() => {
              const q = (libraryQuery || '').trim().toLowerCase();
              if (q.length < 3) {
                return <div style={{ opacity: 0.8 }}></div>;
              }
              if (libraryLoading) return <div className="spinner"></div>;
              const results = libraryItems || [];
              if (results.length === 0) return <div style={{ opacity: 0.8 }}>No results found</div>;
              return results.map((it, idx) => (
                <div key={idx} className="scene-card" style={{ marginBottom: 12 }}>
                  <div className="scene-content" style={{ gap: 12 }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <h4 style={{ margin: 0 }}>{it.title}</h4>
                      <span className="meta-badge">{it.type || 'sfx'}</span>
                    </div>
                    <audio controls style={{ width: '100%' }}>
                      <source src={`http://localhost:3001/collection/audio/${it.path}`} />
                    </audio>
                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <button
                        className="cta-primary"
                        onClick={async () => {
                          try {
                            const res = await fetch(`http://localhost:3001/api/projects/${projectId}/scenes/${sceneId}/shots/${shotId}/content/library`, {
                              method: 'POST',
                              headers: { 'Content-Type': 'application/json' },
                              credentials: 'include',
                              body: JSON.stringify({ path: it.path, type: 'audio', notes: it.title })
                            });
                            const data = await res.json();
                            if (res.ok) {
                              await refreshContents();
                              setShowLibraryModal(false);
                            } else {
                              alert(data.message || 'Failed to add from library');
                            }
                          } catch (e) {
                            console.error('Library add error', e);
                            alert('Connection error while adding from library');
                          }
                        }}
                      >
                        Add to Shot
                      </button>
                    </div>
                  </div>
                </div>
              ));
            })()}
          </div>
        </Modal>
        {/* Drag & Drop overlay */}
        {dragActive && (
          <div className="drop-overlay" onClick={(e) => e.stopPropagation()}>
            <div className="drop-box">
              <i className="fa-solid fa-cloud-arrow-up" style={{ fontSize: 36, marginBottom: 10 }}></i>
              <div>Drop files to upload</div>
              <small>Images, Videos, or Audio</small>
            </div>
          </div>
        )}

        {/* Bottom-left upload tray */}
        {trayVisible && uploadQueue.length > 0 && (
          <div className="upload-tray">
            <div className="upload-tray-header">
              Uploading {uploadsSummary.uploaded + uploadsSummary.uploading} of {uploadsSummary.total} files…
            </div>
            <div className="upload-items">
              {uploadQueue.map(u => (
                <div key={u.id} className={`upload-item ${u.status}`}>
                  <div className="thumb">
                    {u.kind === 'image' ? (
                      <img src={u.preview} alt={u.file.name} />
                    ) : u.kind === 'video' ? (
                      <div className="vthumb"><video src={u.preview} muted /></div>
                    ) : (
                      <div className="athumb"><i className="fa-solid fa-music"></i></div>
                    )}
                  </div>
                  <div className="meta">
                    <div className="name" title={u.file.name}>{u.file.name}</div>
                    <div className="bar"><span style={{ width: `${u.progress}%` }} /></div>
                  </div>
                  <div className="status">
                    {u.status === 'done' ? <i className="fa-solid fa-check"></i> : u.status === 'error' ? <i className="fa-solid fa-triangle-exclamation"></i> : <i className="fa-solid fa-arrow-up"></i>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
        {/* Lightbox for image/video preview */}
        <Modal
          isOpen={!!lightbox}
          onClose={() => setLightbox(null)}
          title={lightbox?.type === 'video' ? 'Preview Video' : 'Preview Image'}
          wide
        >
          {lightbox?.type === 'image' ? (
            <img src={lightbox.src} alt="Preview" style={{ width: '100%', height: 'auto', borderRadius: 12 }} />
          ) : lightbox?.type === 'video' ? (
            <video controls style={{ width: '100%', borderRadius: 12 }}>
              <source src={lightbox.src} />
              Your browser does not support the video tag.
            </video>
          ) : null}
        </Modal>
    </>
  );
}
