import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function Home() {
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    { icon: 'fa-solid fa-clapperboard', title: 'Visual Storytelling', desc: 'Create stunning storyboards with ease' },
    { icon: 'fa-solid fa-palette', title: 'Creative Tools', desc: 'Professional-grade design features' },
    { icon: 'fa-solid fa-rocket', title: 'Fast & Intuitive', desc: 'Streamlined workflow for creators' },
    { icon: 'fa-solid fa-people-group', title: 'Collaborate', desc: 'Work together with your team' }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [features.length]);

  useEffect(() => {
    // Create floating particles for landing page
    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'floating-particle';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.animationDuration = (Math.random() * 3 + 2) + 's';
      particle.style.opacity = Math.random() * 0.3 + 0.1;
      document.body.appendChild(particle);

      setTimeout(() => {
        particle.remove();
      }, 5000);
    };

    const interval = setInterval(createParticle, 500);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="landing-container">
      <div className="landing-background">
        <div className="landing-shape shape-1"></div>
        <div className="landing-shape shape-2"></div>
        <div className="landing-shape shape-3"></div>
        <div className="landing-shape shape-4"></div>
      </div>

      <div className="container">
        <div className="landing-card">
          <div className="landing-header">
            <div className="logo-animation">
              <span className="logo-icon"><i className="fa-solid fa-clapperboard"></i></span>
              <h1>Creative Storyboard</h1>
            </div>
            <p className="landing-subtitle">
              Transform your ideas into visual masterpieces
            </p>
            <p className="landing-description">
              The ultimate platform for creators, filmmakers, and storytellers to bring their visions to life with professional storyboarding tools.
            </p>
          </div>

          <div className="features-showcase">
            <div className="feature-display">
              <div className="feature-icon"><i className={features[currentFeature].icon}></i></div>
              <h3>{features[currentFeature].title}</h3>
              <p>{features[currentFeature].desc}</p>
            </div>
            <div className="feature-dots">
              {features.map((_, index) => (
                <div
                  key={index}
                  className={`dot ${index === currentFeature ? 'active' : ''}`}
                  onClick={() => setCurrentFeature(index)}
                />
              ))}
            </div>
          </div>

          <div className="cta-section">
            <div className="cta-buttons">
              <Link href="/register" className="cta-primary btn-medium">
                <i className="fa-solid fa-rocket" style={{ marginRight: 8 }}></i> Get Started Free
              </Link>
              <Link href="/login" className="cta-secondary btn-medium">
                <i className="fa-solid fa-right-to-bracket" style={{ marginRight: 8 }}></i> Welcome Back
              </Link>
            </div>

            <div className="social-proof">
              <div className="stats-row">
                <div className="stat">
                  <span className="stat-number">10K+</span>
                  <span className="stat-label">Creators</span>
                </div>
                <div className="stat">
                  <span className="stat-number">50K+</span>
                  <span className="stat-label">Storyboards</span>
                </div>
                <div className="stat">
                  <span className="stat-number">100K+</span>
                  <span className="stat-label">Scenes</span>
                </div>
              </div>
            </div>
          </div>

          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon"><i className="fa-solid fa-bolt"></i></div>
              <h4>Lightning Fast</h4>
              <p>Create storyboards in minutes, not hours</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon"><i className="fa-solid fa-bullseye"></i></div>
              <h4>Precision Tools</h4>
              <p>Professional-grade drawing and annotation tools</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon"><i className="fa-solid fa-cloud"></i></div>
              <h4>Cloud Sync</h4>
              <p>Access your work anywhere, anytime</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon"><i className="fa-solid fa-handshake"></i></div>
              <h4>Team Ready</h4>
              <p>Collaborate seamlessly with your team</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
