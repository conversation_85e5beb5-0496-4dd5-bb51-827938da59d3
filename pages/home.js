import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../components/Navbar';

export default function HomePage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [stats, setStats] = useState({
    projects: { total: 0, this_week: 0 },
    scenes: { total: 0, this_week: 0 },
    shots: { total: 0, this_week: 0 }
  });
  const [activities, setActivities] = useState([]);
  const router = useRouter();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (res.ok) {
          const data = await res.json();
          setUser(data);
          // Load stats after auth success
          try {
            const sr = await fetch('http://localhost:3001/api/stats', { credentials: 'include' });
            if (sr.ok) {
              const sd = await sr.json();
              setStats(sd);
            }
          } catch (e) {
            console.warn('Failed to load stats', e);
          }
          // Load recent activities
          try {
            const ar = await fetch('http://localhost:3001/api/activities?limit=10', { credentials: 'include' });
            if (ar.ok) {
              const ad = await ar.json();
              setActivities(ad.activities || []);
            }
          } catch (e) {
            console.warn('Failed to load activities', e);
          }
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();

    // Update time every minute
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timeInterval);
  }, [router]);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const timeAgo = (dateString) => {
    const then = new Date(dateString).getTime();
    const now = Date.now();
    const diff = Math.max(0, now - then);
    const sec = Math.floor(diff / 1000);
    if (sec < 60) return `${sec}s ago`;
    const min = Math.floor(sec / 60);
    if (min < 60) return `${min}m ago`;
    const hr = Math.floor(min / 60);
    if (hr < 24) return `${hr}h ago`;
    const day = Math.floor(hr / 24);
    return `${day}d ago`;
  };

  const iconForActivity = (a) => {
    if (!a) return 'fa-regular fa-clock';
    const t = (a.activity || '').toLowerCase();
    if (t.startsWith('created project')) return 'fa-solid fa-folder-plus';
    if (t.startsWith('updated project')) return 'fa-solid fa-pen-to-square';
    if (t.startsWith('deleted project')) return 'fa-solid fa-trash';
    if (t.includes('scene')) {
      if (t.startsWith('created')) return 'fa-solid fa-clapperboard';
      if (t.startsWith('updated')) return 'fa-solid fa-pen';
      if (t.startsWith('deleted')) return 'fa-solid fa-trash';
    }
    if (t.includes('shot')) {
      if (t.startsWith('created')) return 'fa-solid fa-camera';
      if (t.startsWith('updated')) return 'fa-solid fa-pen';
      if (t.startsWith('deleted')) return 'fa-solid fa-trash';
    }
    if (t.includes('uploaded')) return 'fa-solid fa-upload';
    if (t.includes('content')) return 'fa-solid fa-box-archive';
    return 'fa-regular fa-clock';
  };

  const linkForActivity = (a) => {
    const p = a.project_id;
    const s = a.scene_id;
    const sh = a.shot_id;
    if (p && s && sh) return `/projects/${p}/scenes/${s}/shots/${sh}`;
    if (p && s) return `/projects/${p}/scenes/${s}`;
    if (p) return `/projects/${p}`;
    return null;
  };

  const formatTime = () => {
    return currentTime.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = () => {
    return currentTime.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container container-top">
        <div className="dashboard-container">
          {/* Welcome Header */}
          <div className="welcome-header">
            <div className="welcome-content">
              <h1>{getGreeting()}, {user?.username}! <i className="fa-regular fa-star" aria-hidden="true"></i></h1>
              <p>Ready to create something amazing today?</p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="dashboard-grid">
            <div className="dashboard-card stats-card">
              <div className="card-icon"><i className="fa-solid fa-folder"></i></div>
              <div className="card-content">
                <h3>Projects</h3>
                <div className="stat-number">{stats.projects.total}</div>
                <p className="stat-change">+{stats.projects.this_week} this week</p>
              </div>
            </div>

            <div className="dashboard-card stats-card">
              <div className="card-icon"><i className="fa-solid fa-clapperboard"></i></div>
              <div className="card-content">
                <h3>Scenes</h3>
                <div className="stat-number">{stats.scenes.total}</div>
                <p className="stat-change">+{stats.scenes.this_week} this week</p>
              </div>
            </div>

            <div className="dashboard-card stats-card">
              <div className="card-icon"><i className="fa-solid fa-camera"></i></div>
              <div className="card-content">
                <h3>Shots</h3>
                <div className="stat-number">{stats.shots.total}</div>
                <p className="stat-change">+{stats.shots.this_week} this week</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="dashboard-grid">
            <div className="dashboard-card recent-card">
              <div className="card-header">
                <h3><i className="fa-regular fa-pen-to-square" style={{ marginRight: 8 }}></i> Recent Activity</h3>
              </div>
              <div className="activity-list">
                {activities.length === 0 ? (
                  <div className="activity-item" style={{ justifyContent: 'center' }}>
                    <div className="activity-content">
                      <p>No recent activity.</p>
                    </div>
                  </div>
                ) : (
                  activities.map((a) => {
                    const to = linkForActivity(a);
                    const props = to
                      ? {
                          role: 'button',
                          tabIndex: 0,
                          onClick: () => router.push(to),
                          onKeyDown: (e) => {
                            if (e.key === 'Enter' || e.key === ' ') router.push(to);
                          }
                        }
                      : {};
                    return (
                      <div className={`activity-item ${to ? 'is-link' : ''}`} key={a.id} {...props}>
                        <div className="activity-icon"><i className={iconForActivity(a)}></i></div>
                        <div className="activity-content">
                          <p>{a.activity}</p>
                          <span className="activity-time">{timeAgo(a.created_at)}</span>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Floating Action Button */}
        {/* <button className="fab" title="Create New">
          <i className="fa-solid fa-plus"></i>
        </button> */}
      </div>
    </>
  );
}
