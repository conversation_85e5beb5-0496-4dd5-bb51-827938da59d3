const express = require('express');
const bodyParser = require('body-parser');
const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const cors = require('cors');
const crypto = require('crypto');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const app = express();
app.use(cors({ origin: 'http://localhost:3000', credentials: true }));
app.use(bodyParser.json());

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
// Serve audio library (JSON index and audio files)
app.use('/collection/audio', express.static(path.join(__dirname, 'collection', 'audio')));

// Audio library search (reads JSON index and filters server-side)
app.get('/api/library/audio', async (req, res) => {
  try {
    const qraw = (req.query.q || '').toString();
    const limit = Math.min(parseInt(req.query.limit, 10) || 100, 500);
    if (!qraw || qraw.trim().length < 3) return res.json({ items: [] });

    const indexPath = path.join(__dirname, 'collection', 'audio', 'index.json');
    if (!fs.existsSync(indexPath)) return res.json({ items: [] });
    const raw = await fs.promises.readFile(indexPath, 'utf8');
    let list = [];
    try { list = JSON.parse(raw) || []; } catch (_) { list = []; }

    // Fuzzy matching with light stemming and token similarity
    const norm = (s) => (s || '').toLowerCase().replace(/[^a-z0-9]+/g, ' ').trim();
    const stem = (t) => {
      if (!t) return t;
      // very light stemming for common endings
      return t
        .replace(/(ing|ers|er|ed|ly)$/i, '')
        .replace(/(ies)$/i, 'y')
        .replace(/(s)$/i, (m) => (t.length > 3 ? '' : m));
    };
    const toks = (s) => norm(s).split(/\s+/).filter(Boolean).map(stem);
    const lev = (a, b) => {
      if (a === b) return 0;
      const al = a.length, bl = b.length;
      if (al === 0) return bl;
      if (bl === 0) return al;
      const dp = Array(bl + 1).fill(0).map((_, i) => i);
      for (let i = 1; i <= al; i++) {
        let prev = i - 1;
        dp[0] = i;
        for (let j = 1; j <= bl; j++) {
          const tmp = dp[j];
          const cost = a[i - 1] === b[j - 1] ? 0 : 1;
          dp[j] = Math.min(
            dp[j] + 1,
            dp[j - 1] + 1,
            prev + cost
          );
          prev = tmp;
        }
      }
      return dp[bl];
    };
    const simTok = (a, b) => {
      if (!a || !b) return 0;
      if (a === b) return 1;
      if (a.includes(b) || b.includes(a)) return 0.92;
      const d = lev(a, b);
      const maxl = Math.max(a.length, b.length);
      return 1 - d / maxl;
    };

    const q = norm(qraw);
    const qTokens = toks(q);
    // quick prefilter by simple substring to reduce workload
    const coarse = list.filter(it => (it.index || '').toLowerCase().includes(q) || qTokens.some(t => (it.index || '').includes(t)));
    const scored = (coarse.length ? coarse : list).map(it => {
      const itIndex = norm(it.index || it.title || '');
      const itTokens = toks(itIndex);
      if (itTokens.length === 0 || qTokens.length === 0) return { it, score: 0 };
      let sum = 0;
      for (const qt of qTokens) {
        let best = 0;
        for (const tt of itTokens) {
          const s = simTok(qt, tt);
          if (s > best) best = s;
          if (best >= 0.99) break;
        }
        sum += best;
      }
      let score = sum / qTokens.length;
      if (itIndex.includes(q)) score += 0.05; // slight boost for phrase
      return { it, score };
    })
    .filter(x => x.score >= 0.55)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(x => x.it);

    res.json({ items: scored });
  } catch (e) {
    console.error('Library search error:', e);
    res.status(500).json({ items: [] });
  }
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, 'uploads', 'projects');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'project-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed!'));
    }
  }
});

// Configure multer for shot content uploads (images, audio, video)
const shotContentStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(__dirname, 'uploads', 'shot-content');
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const { shotId } = req.params || {};
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `shot-${shotId || 'unknown'}-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const allowedContentTypes = [
  // images
  'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
  // audio
  'audio/mpeg', 'audio/wav', 'audio/x-wav', 'audio/ogg', 'audio/mp4', 'audio/aac', 'audio/x-m4a', 'audio/webm',
  // video
  'video/mp4', 'video/quicktime', 'video/webm', 'video/ogg'
];

const shotContentUpload = multer({
  storage: shotContentStorage,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit for media
  },
  fileFilter: (req, file, cb) => {
    if (allowedContentTypes.includes(file.mimetype)) {
      return cb(null, true);
    }
    cb(new Error('Unsupported file type for shot content'));
  }
});

const sessions = {};

function parseCookies(cookieHeader = '') {
  const cookies = {};
  cookieHeader.split(';').forEach(cookie => {
    const [name, ...rest] = cookie.trim().split('=');
    if (!name) return;
    cookies[name] = decodeURIComponent(rest.join('='));
  });
  return cookies;
}

function authMiddleware(req, res, next) {
  const cookies = parseCookies(req.headers.cookie);
  const session = sessions[cookies.sessionId];
  if (session) {
    req.user = session;
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
}

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
});

// Helper: log user activity
async function logActivity({ userId, projectId = null, sceneId = null, shotId = null, shotContentId = null, activity }) {
  try {
    await pool.query(
      'INSERT INTO activities (project_id, scene_id, shot_id, shot_content_id, user_id, activity) VALUES (?, ?, ?, ?, ?, ?)',
      [projectId, sceneId, shotId, shotContentId, userId, activity?.slice(0, 255) || '']
    );
  } catch (e) {
    console.warn('Failed to log activity:', e.message);
  }
}

app.post('/api/register', async (req, res) => {
  const { username, email, password } = req.body;
  if (!username || !email || !password) {
    return res.status(400).json({ message: 'Missing fields' });
  }
  try {
    const hashed = await bcrypt.hash(password, 10);
    await pool.query('INSERT INTO users (username, email, password) VALUES (?, ?, ?)', [username, email, hashed]);
    res.json({ message: 'User registered successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Registration failed' });
  }
});

app.post('/api/login', async (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    return res.status(400).json({ message: 'Missing fields' });
  }
  try {
    const [rows] = await pool.query('SELECT * FROM users WHERE email = ?', [email]);
    if (rows.length === 0) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    const user = rows[0];
    const match = await bcrypt.compare(password, user.password);
    if (!match) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    const sessionId = crypto.randomBytes(16).toString('hex');
    sessions[sessionId] = { id: user.id, username: user.username, email: user.email };
    res.cookie('sessionId', sessionId, { httpOnly: true });
    res.json({ message: 'Login successful', username: user.username });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Login failed' });
  }
});

app.get('/api/home', authMiddleware, (req, res) => {
  res.json({ username: req.user.username, email: req.user.email });
});

app.post('/api/logout', (req, res) => {
  const cookies = parseCookies(req.headers.cookie);
  const sessionId = cookies.sessionId;

  if (sessionId && sessions[sessionId]) {
    delete sessions[sessionId];
  }

  res.clearCookie('sessionId');
  res.json({ message: 'Logout successful' });
});

// Aggregate stats for dashboard
app.get('/api/stats', authMiddleware, async (req, res) => {
  try {
    const userId = req.user.id;

    // Projects
    const [projRows] = await pool.query(
      'SELECT COUNT(*) AS cnt FROM projects WHERE user_id = ?',
      [userId]
    );
    const totalProjects = projRows[0]?.cnt ?? 0;
    const [projWeekRows] = await pool.query(
      'SELECT COUNT(*) AS cnt FROM projects WHERE user_id = ? AND created_at >= DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 7 DAY)',
      [userId]
    );
    const projectsThisWeek = projWeekRows[0]?.cnt ?? 0;

    // Scenes (owned through projects)
    const [sceneRows] = await pool.query(
      `SELECT COUNT(*) AS cnt FROM scenes 
       WHERE project_id IN (SELECT id FROM projects WHERE user_id = ?)`,
      [userId]
    );
    const totalScenes = sceneRows[0]?.cnt ?? 0;
    const [sceneWeekRows] = await pool.query(
      `SELECT COUNT(*) AS cnt FROM scenes 
       WHERE project_id IN (SELECT id FROM projects WHERE user_id = ?)
         AND created_at >= DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 7 DAY)`,
      [userId]
    );
    const scenesThisWeek = sceneWeekRows[0]?.cnt ?? 0;

    // Shots (owned through scenes -> projects)
    const [shotRows] = await pool.query(
      `SELECT COUNT(*) AS cnt FROM shots 
       WHERE scene_id IN (
         SELECT id FROM scenes WHERE project_id IN (SELECT id FROM projects WHERE user_id = ?)
       )`,
      [userId]
    );
    const totalShots = shotRows[0]?.cnt ?? 0;
    const [shotWeekRows] = await pool.query(
      `SELECT COUNT(*) AS cnt FROM shots 
       WHERE scene_id IN (
         SELECT id FROM scenes WHERE project_id IN (SELECT id FROM projects WHERE user_id = ?)
       )
         AND created_at >= DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 7 DAY)`,
      [userId]
    );
    const shotsThisWeek = shotWeekRows[0]?.cnt ?? 0;

    res.json({
      projects: { total: Number(totalProjects) || 0, this_week: Number(projectsThisWeek) || 0 },
      scenes: { total: Number(totalScenes) || 0, this_week: Number(scenesThisWeek) || 0 },
      shots: { total: Number(totalShots) || 0, this_week: Number(shotsThisWeek) || 0 }
    });
  } catch (err) {
    console.error('Error computing stats:', err);
    res.status(500).json({ message: 'Failed to load stats' });
  }
});

// Recent activities for the authenticated user
app.get('/api/activities', authMiddleware, async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = Math.min(parseInt(req.query.limit || '10', 10) || 10, 50);
    const [rows] = await pool.query(
      `SELECT id, project_id, scene_id, shot_id, shot_content_id, activity, created_at
       FROM activities
       WHERE user_id = ?
       ORDER BY created_at DESC
       LIMIT ?`,
      [userId, limit]
    );
    res.json({ activities: rows });
  } catch (err) {
    console.error('Error fetching activities:', err);
    res.status(500).json({ message: 'Failed to fetch activities' });
  }
});

// Function to generate random pattern for projects without images
function generateRandomPattern() {
  const patterns = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',
    'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)'
  ];
  return patterns[Math.floor(Math.random() * patterns.length)];
}

// Create new project
app.post('/api/projects', authMiddleware, upload.single('image'), async (req, res) => {
  const { title, description } = req.body;

  if (!title || title.trim() === '') {
    return res.status(400).json({ message: 'Project title is required' });
  }

  try {
    let imagePath = null;

    if (req.file) {
      // If image was uploaded, save the relative path
      imagePath = `/uploads/projects/${req.file.filename}`;
    } else {
      // If no image, generate a random pattern
      imagePath = generateRandomPattern();
    }

    const [result] = await pool.query(
      'INSERT INTO projects (user_id, title, description, image) VALUES (?, ?, ?, ?)',
      [req.user.id, title.trim(), description || null, imagePath]
    );

    // Log activity
    logActivity({
      userId: req.user.id,
      projectId: result.insertId,
      activity: `Created project: ${title.trim()}`
    });

    res.json({
      message: 'Project created successfully',
      projectId: result.insertId,
      imagePath: imagePath
    });
  } catch (err) {
    console.error('Error creating project:', err);
    res.status(500).json({ message: 'Failed to create project' });
  }
});

// Get user's projects
app.get('/api/projects', authMiddleware, async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT 
         p.id, p.title, p.description, p.image, p.created_at, p.updated_at,
         (SELECT COUNT(*) FROM scenes s WHERE s.project_id = p.id) AS scenes_count
       FROM projects p
       WHERE p.user_id = ?
       ORDER BY p.updated_at DESC`,
      [req.user.id]
    );

    res.json({ projects: rows });
  } catch (err) {
    console.error('Error fetching projects:', err);
    res.status(500).json({ message: 'Failed to fetch projects' });
  }
});

// Get single project details
app.get('/api/projects/:id', authMiddleware, async (req, res) => {
  const projectId = req.params.id;

  try {
    const [rows] = await pool.query(
      'SELECT id, title, description, image, created_at, updated_at FROM projects WHERE id = ? AND user_id = ?',
      [projectId, req.user.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: 'Project not found or access denied' });
    }

    res.json({ project: rows[0] });
  } catch (err) {
    console.error('Error fetching project:', err);
    res.status(500).json({ message: 'Failed to fetch project' });
  }
});

// Update project
app.put('/api/projects/:id', authMiddleware, upload.single('image'), async (req, res) => {
  const projectId = req.params.id;
  const { title, description, keepCurrentImage } = req.body;

  if (!title || title.trim() === '') {
    return res.status(400).json({ message: 'Project title is required' });
  }

  try {
    // First, verify the project belongs to the user
    const [existingProject] = await pool.query(
      'SELECT * FROM projects WHERE id = ? AND user_id = ?',
      [projectId, req.user.id]
    );

    if (existingProject.length === 0) {
      return res.status(404).json({ message: 'Project not found or access denied' });
    }

    let imagePath = existingProject[0].image; // Keep current image by default

    if (req.file) {
      // If new image was uploaded, save the relative path
      imagePath = `/uploads/projects/${req.file.filename}`;

      // Delete old image file if it exists and is not a pattern
      const oldImagePath = existingProject[0].image;
      if (oldImagePath && oldImagePath.startsWith('/uploads')) {
        const fullOldPath = path.join(__dirname, oldImagePath);
        if (fs.existsSync(fullOldPath)) {
          fs.unlinkSync(fullOldPath);
        }
      }
    } else if (keepCurrentImage !== 'true') {
      // If no new image and not keeping current, generate a random pattern
      imagePath = generateRandomPattern();

      // Delete old image file if it exists and is not a pattern
      const oldImagePath = existingProject[0].image;
      if (oldImagePath && oldImagePath.startsWith('/uploads')) {
        const fullOldPath = path.join(__dirname, oldImagePath);
        if (fs.existsSync(fullOldPath)) {
          fs.unlinkSync(fullOldPath);
        }
      }
    }

    await pool.query(
      'UPDATE projects SET title = ?, description = ?, image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      [title.trim(), description || null, imagePath, projectId, req.user.id]
    );

    // Log activity
    logActivity({
      userId: req.user.id,
      projectId,
      activity: `Updated project: ${title.trim()}`
    });

    res.json({
      message: 'Project updated successfully',
      imagePath: imagePath
    });
  } catch (err) {
    console.error('Error updating project:', err);
    res.status(500).json({ message: 'Failed to update project' });
  }
});

// Delete project (and cascade scenes, shots, shot content). Also remove related uploaded files.
app.delete('/api/projects/:id', authMiddleware, async (req, res) => {
  const projectId = req.params.id;
  try {
    // Verify ownership and fetch project image/title
    const [projRows] = await pool.query(
      'SELECT id, title, image FROM projects WHERE id = ? AND user_id = ?',
      [projectId, req.user.id]
    );
    if (projRows.length === 0) {
      return res.status(404).json({ message: 'Project not found or access denied' });
    }

    // Gather all shot_content files under this project before deletion
    const [contentRows] = await pool.query(
      `SELECT sc.file_path FROM shot_content sc
       JOIN shots sh ON sc.shot_id = sh.id
       JOIN scenes s ON sh.scene_id = s.id
       WHERE s.project_id = ?`,
      [projectId]
    );

    // Delete the project (cascades to scenes, shots, shot_content via FK constraints)
    await pool.query('DELETE FROM projects WHERE id = ? AND user_id = ?', [projectId, req.user.id]);

    // Remove project banner image if it's an uploaded file
    const imagePath = projRows[0].image;
    if (imagePath && imagePath.startsWith('/uploads')) {
      const fullOldPath = path.join(__dirname, imagePath);
      if (fs.existsSync(fullOldPath)) {
        try { fs.unlinkSync(fullOldPath); } catch (e) { console.warn('Failed to remove project image:', e.message); }
      }
    }

    // Remove shot content files from disk
    for (const row of contentRows) {
      const fp = row.file_path;
      if (!fp) continue;
      // Never delete shared library files
      if (fp.startsWith('/collection/')) {
        console.info('Skipping deletion of shared library file during project delete', { projectId, filePath: fp });
        continue;
      }
      if (fp.startsWith('/uploads')) {
        const fullPath = path.join(__dirname, fp);
        if (fs.existsSync(fullPath)) {
          try { fs.unlinkSync(fullPath); } catch (e) { console.warn('Failed to remove content file:', e.message); }
        }
      } else {
        console.warn('Skipping deletion for unknown file path pattern during project delete', { projectId, filePath: fp });
      }
    }

    // Log activity after successful deletion
    logActivity({
      userId: req.user.id,
      projectId: null,
      activity: `Deleted project: ${projRows[0].title}`
    });

    res.json({ message: 'Project and related data deleted successfully' });
  } catch (err) {
    console.error('Error deleting project:', err);
    res.status(500).json({ message: 'Failed to delete project' });
  }
});

// Create new scene
app.post('/api/projects/:projectId/scenes', authMiddleware, async (req, res) => {
  const projectId = req.params.projectId;
  const { title, description } = req.body;

  if (!title || title.trim() === '') {
    return res.status(400).json({ message: 'Scene title is required' });
  }

  try {
    // First, verify the project belongs to the user
    const [projectRows] = await pool.query(
      'SELECT id FROM projects WHERE id = ? AND user_id = ?',
      [projectId, req.user.id]
    );

    if (projectRows.length === 0) {
      return res.status(404).json({ message: 'Project not found or access denied' });
    }

    const [result] = await pool.query(
      'INSERT INTO scenes (project_id, title, description) VALUES (?, ?, ?)',
      [projectId, title.trim(), description || null]
    );

    // Log activity
    logActivity({
      userId: req.user.id,
      projectId,
      sceneId: result.insertId,
      activity: `Created scene: ${title.trim()}`
    });

    res.json({
      message: 'Scene created successfully',
      sceneId: result.insertId
    });
  } catch (err) {
    console.error('Error creating scene:', err);
    res.status(500).json({ message: 'Failed to create scene' });
  }
});

// Get scenes for a project
app.get('/api/projects/:projectId/scenes', authMiddleware, async (req, res) => {
  const projectId = req.params.projectId;

  try {
    // First, verify the project belongs to the user
    const [projectRows] = await pool.query(
      'SELECT id FROM projects WHERE id = ? AND user_id = ?',
      [projectId, req.user.id]
    );

    if (projectRows.length === 0) {
      return res.status(404).json({ message: 'Project not found or access denied' });
    }

    const [rows] = await pool.query(
      'SELECT id, title, description, created_at, updated_at FROM scenes WHERE project_id = ? ORDER BY created_at ASC',
      [projectId]
    );

    res.json({ scenes: rows });
  } catch (err) {
    console.error('Error fetching scenes:', err);
    res.status(500).json({ message: 'Failed to fetch scenes' });
  }
});

// Update scene
app.put('/api/projects/:projectId/scenes/:sceneId', authMiddleware, async (req, res) => {
  const projectId = req.params.projectId;
  const sceneId = req.params.sceneId;
  const { title, description } = req.body;

  if (!title || title.trim() === '') {
    return res.status(400).json({ message: 'Scene title is required' });
  }

  try {
    // First, verify the project belongs to the user and the scene belongs to the project
    const [sceneRows] = await pool.query(
      `SELECT s.id FROM scenes s
       JOIN projects p ON s.project_id = p.id
       WHERE s.id = ? AND s.project_id = ? AND p.user_id = ?`,
      [sceneId, projectId, req.user.id]
    );

    if (sceneRows.length === 0) {
      return res.status(404).json({ message: 'Scene not found or access denied' });
    }

    await pool.query(
      'UPDATE scenes SET title = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [title.trim(), description || null, sceneId]
    );

    // Log activity
    logActivity({
      userId: req.user.id,
      projectId,
      sceneId,
      activity: `Updated scene: ${title.trim()}`
    });

    res.json({
      message: 'Scene updated successfully'
    });
  } catch (err) {
    console.error('Error updating scene:', err);
    res.status(500).json({ message: 'Failed to update scene' });
  }
});

// Get single scene details
app.get('/api/projects/:projectId/scenes/:sceneId', authMiddleware, async (req, res) => {
  const projectId = req.params.projectId;
  const sceneId = req.params.sceneId;

  try {
    // Verify the scene belongs to a project owned by the user
    const [rows] = await pool.query(
      `SELECT s.id, s.title, s.description, s.created_at, s.updated_at
       FROM scenes s
       JOIN projects p ON s.project_id = p.id
       WHERE s.id = ? AND s.project_id = ? AND p.user_id = ?`,
      [sceneId, projectId, req.user.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: 'Scene not found or access denied' });
    }

    res.json({ scene: rows[0] });
  } catch (err) {
    console.error('Error fetching scene:', err);
    res.status(500).json({ message: 'Failed to fetch scene' });
  }
});

// Create new shot
app.post('/api/projects/:projectId/scenes/:sceneId/shots', authMiddleware, async (req, res) => {
  const projectId = req.params.projectId;
  const sceneId = req.params.sceneId;
  const { title, content, ai_prompt } = req.body;

  if (!title || title.trim() === '') {
    return res.status(400).json({ message: 'Shot title is required' });
  }

  try {
    // Verify the scene belongs to a project owned by the user
    const [sceneRows] = await pool.query(
      `SELECT s.id FROM scenes s
       JOIN projects p ON s.project_id = p.id
       WHERE s.id = ? AND s.project_id = ? AND p.user_id = ?`,
      [sceneId, projectId, req.user.id]
    );

    if (sceneRows.length === 0) {
      return res.status(404).json({ message: 'Scene not found or access denied' });
    }

    // Get the next ordering number for this scene
    const [orderRows] = await pool.query(
      'SELECT COALESCE(MAX(ordering), 0) + 1 as next_order FROM shots WHERE scene_id = ?',
      [sceneId]
    );
    const nextOrder = orderRows[0].next_order;

    const [result] = await pool.query(
      'INSERT INTO shots (scene_id, title, content, ai_prompt, ordering) VALUES (?, ?, ?, ?, ?)',
      [sceneId, title.trim(), content || null, ai_prompt || null, nextOrder]
    );

    // Log activity
    logActivity({
      userId: req.user.id,
      projectId,
      sceneId,
      shotId: result.insertId,
      activity: `Created shot: ${title.trim()}`
    });

    res.json({
      message: 'Shot created successfully',
      shotId: result.insertId
    });
  } catch (err) {
    console.error('Error creating shot:', err);
    res.status(500).json({ message: 'Failed to create shot' });
  }
});

// Get shots for a scene
app.get('/api/projects/:projectId/scenes/:sceneId/shots', authMiddleware, async (req, res) => {
  const projectId = req.params.projectId;
  const sceneId = req.params.sceneId;

  try {
    // Verify the scene belongs to a project owned by the user
    const [sceneRows] = await pool.query(
      `SELECT s.id FROM scenes s
       JOIN projects p ON s.project_id = p.id
       WHERE s.id = ? AND s.project_id = ? AND p.user_id = ?`,
      [sceneId, projectId, req.user.id]
    );

    if (sceneRows.length === 0) {
      return res.status(404).json({ message: 'Scene not found or access denied' });
    }

    const [rows] = await pool.query(
      `SELECT 
         sh.id, sh.title, sh.content, sh.ai_prompt, sh.ordering, sh.created_at, sh.updated_at,
         COUNT(sc.id) AS content_count
       FROM shots sh
       LEFT JOIN shot_content sc ON sc.shot_id = sh.id
       WHERE sh.scene_id = ?
       GROUP BY sh.id, sh.title, sh.content, sh.ai_prompt, sh.ordering, sh.created_at, sh.updated_at
       ORDER BY sh.ordering ASC`,
      [sceneId]
    );

    res.json({ shots: rows });
  } catch (err) {
    console.error('Error fetching shots:', err);
    res.status(500).json({ message: 'Failed to fetch shots' });
  }
});

// Update shot
app.put('/api/projects/:projectId/scenes/:sceneId/shots/:shotId', authMiddleware, async (req, res) => {
  const projectId = req.params.projectId;
  const sceneId = req.params.sceneId;
  const shotId = req.params.shotId;
  const { title, content, ai_prompt } = req.body;

  if (!title || title.trim() === '') {
    return res.status(400).json({ message: 'Shot title is required' });
  }

  try {
    // Verify the shot belongs to a scene in a project owned by the user
    const [shotRows] = await pool.query(
      `SELECT sh.id FROM shots sh
       JOIN scenes s ON sh.scene_id = s.id
       JOIN projects p ON s.project_id = p.id
       WHERE sh.id = ? AND sh.scene_id = ? AND s.project_id = ? AND p.user_id = ?`,
      [shotId, sceneId, projectId, req.user.id]
    );

    if (shotRows.length === 0) {
      return res.status(404).json({ message: 'Shot not found or access denied' });
    }

    await pool.query(
      'UPDATE shots SET title = ?, content = ?, ai_prompt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [title.trim(), content || null, ai_prompt || null, shotId]
    );

    // Log activity
    logActivity({
      userId: req.user.id,
      projectId,
      sceneId,
      shotId,
      activity: `Updated shot: ${title.trim()}`
    });

    res.json({
      message: 'Shot updated successfully'
    });
  } catch (err) {
    console.error('Error updating shot:', err);
    res.status(500).json({ message: 'Failed to update shot' });
  }
});

// Add shot content (image, audio, or video)
app.post(
  '/api/projects/:projectId/scenes/:sceneId/shots/:shotId/content',
  authMiddleware,
  shotContentUpload.single('file'),
  async (req, res) => {
    const { projectId, sceneId, shotId } = req.params;
    const { content_type, notes } = req.body;

    try {
      // Verify the shot belongs to the authenticated user
      const [shotRows] = await pool.query(
        `SELECT sh.id FROM shots sh
         JOIN scenes s ON sh.scene_id = s.id
         JOIN projects p ON s.project_id = p.id
         WHERE sh.id = ? AND sh.scene_id = ? AND s.project_id = ? AND p.user_id = ?`,
        [shotId, sceneId, projectId, req.user.id]
      );

      if (shotRows.length === 0) {
        return res.status(404).json({ message: 'Shot not found or access denied' });
      }

      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      // Determine content type if not explicitly provided
      let derivedType = content_type;
      if (!derivedType) {
        if (req.file.mimetype.startsWith('image/')) derivedType = 'image';
        else if (req.file.mimetype.startsWith('audio/')) derivedType = 'audio';
        else if (req.file.mimetype.startsWith('video/')) derivedType = 'video';
      }

      if (!['image', 'audio', 'video'].includes(derivedType)) {
        return res.status(400).json({ message: 'Invalid or missing content_type' });
      }

      const filePath = `/uploads/shot-content/${req.file.filename}`;
      let safeNotes = notes && notes.length > 0 ? notes.substring(0, 255) : null;
      // If uploading audio with no provided notes, default to original filename for context
      if (!safeNotes && derivedType === 'audio' && req.file?.originalname) {
        try {
          safeNotes = path.basename(req.file.originalname).slice(0, 255);
        } catch (_) {
          // ignore fallback failures
        }
      }

      const [result] = await pool.query(
        'INSERT INTO shot_content (shot_id, content_type, file_path, notes) VALUES (?, ?, ?, ?)',
        [shotId, derivedType, filePath, safeNotes]
      );

      // Log activity
      logActivity({
        userId: req.user.id,
        projectId,
        sceneId,
        shotId,
        shotContentId: result.insertId,
        activity: `Uploaded ${derivedType} to shot ${shotId}`
      });

      res.json({
        message: 'Shot content added successfully',
        contentId: result.insertId,
        filePath,
        contentType: derivedType,
        notes: safeNotes
      });
    } catch (err) {
      console.error('Error adding shot content:', err);
      res.status(500).json({ message: 'Failed to add shot content' });
    }
  }
);

// Add content from audio library without re-uploading
app.post('/api/projects/:projectId/scenes/:sceneId/shots/:shotId/content/library', authMiddleware, async (req, res) => {
  const { projectId, sceneId, shotId } = req.params;
  const { path: relPath, type = 'audio', notes } = req.body || {};

  try {
    // Verify the shot belongs to the authenticated user
    const [shotRows] = await pool.query(
      `SELECT sh.id FROM shots sh
       JOIN scenes s ON sh.scene_id = s.id
       JOIN projects p ON s.project_id = p.id
       WHERE sh.id = ? AND sh.scene_id = ? AND s.project_id = ? AND p.user_id = ?`,
      [shotId, sceneId, projectId, req.user.id]
    );
    if (shotRows.length === 0) {
      return res.status(404).json({ message: 'Shot not found or access denied' });
    }

    if (!relPath || typeof relPath !== 'string') {
      return res.status(400).json({ message: 'Missing library path' });
    }

    // Only allow audio for now
    const derivedType = (type || 'audio').toLowerCase();
    if (derivedType !== 'audio') {
      return res.status(400).json({ message: 'Only audio library items are supported at this time' });
    }

    // Normalize and ensure path stays within collection/audio
    const libRoot = path.join(__dirname, 'collection', 'audio');
    const abs = path.join(libRoot, relPath);
    const norm = path.normalize(abs);
    if (!norm.startsWith(libRoot)) {
      return res.status(400).json({ message: 'Invalid library path' });
    }
    // Check file exists
    if (!fs.existsSync(norm) || !fs.statSync(norm).isFile()) {
      return res.status(404).json({ message: 'Library file not found' });
    }

    const urlPath = '/collection/audio/' + relPath.replace(/\\/g, '/');
    const safeNotes = (notes && notes.trim()) ? notes.trim().slice(0, 255) : path.basename(relPath).slice(0, 255);

    const [result] = await pool.query(
      'INSERT INTO shot_content (shot_id, content_type, file_path, notes) VALUES (?, ?, ?, ?)',
      [shotId, derivedType, urlPath, safeNotes]
    );

    // Log activity
    logActivity({
      userId: req.user.id,
      projectId,
      sceneId,
      shotId,
      shotContentId: result.insertId,
      activity: `Added ${derivedType} from library to shot ${shotId}`
    });

    res.json({
      message: 'Shot content added from library',
      contentId: result.insertId,
      filePath: urlPath,
      contentType: derivedType,
      notes: safeNotes
    });
  } catch (err) {
    console.error('Error adding library content:', err);
    res.status(500).json({ message: 'Failed to add library content' });
  }
});

// Get all content items for a shot
app.get('/api/projects/:projectId/scenes/:sceneId/shots/:shotId/content', authMiddleware, async (req, res) => {
  const { projectId, sceneId, shotId } = req.params;
  try {
    const [shotRows] = await pool.query(
      `SELECT sh.id FROM shots sh
       JOIN scenes s ON sh.scene_id = s.id
       JOIN projects p ON s.project_id = p.id
       WHERE sh.id = ? AND sh.scene_id = ? AND s.project_id = ? AND p.user_id = ?`,
      [shotId, sceneId, projectId, req.user.id]
    );

    if (shotRows.length === 0) {
      return res.status(404).json({ message: 'Shot not found or access denied' });
    }

    const [rows] = await pool.query(
      `SELECT id, shot_id, content_type, file_path, notes, created_at, updated_at
       FROM shot_content
       WHERE shot_id = ?
       ORDER BY created_at ASC`,
      [shotId]
    );

    res.json({ contents: rows });
  } catch (err) {
    console.error('Error fetching shot content:', err);
    res.status(500).json({ message: 'Failed to fetch shot content' });
  }
});

// Delete a content item from a shot
app.delete('/api/projects/:projectId/scenes/:sceneId/shots/:shotId/content/:contentId', authMiddleware, async (req, res) => {
  const { projectId, sceneId, shotId, contentId } = req.params;
  try {
    // Verify ownership via join and fetch file path for deletion if exists
    const [rows] = await pool.query(
      `SELECT sc.id, sc.file_path, sc.content_type FROM shot_content sc
       JOIN shots sh ON sc.shot_id = sh.id
       JOIN scenes s ON sh.scene_id = s.id
       JOIN projects p ON s.project_id = p.id
       WHERE sc.id = ? AND sc.shot_id = ? AND sh.scene_id = ? AND s.project_id = ? AND p.user_id = ?`,
      [contentId, shotId, sceneId, projectId, req.user.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: 'Content not found or access denied' });
    }

    const filePath = rows[0].file_path;
    await pool.query('DELETE FROM shot_content WHERE id = ?', [contentId]);

    // Remove uploaded files only; never delete shared library files
    if (filePath && filePath.startsWith('/collection/')) {
      console.info('Skipping deletion of shared library file for content delete', { contentId, filePath });
    } else if (filePath && filePath.startsWith('/uploads')) {
      const fullPath = path.join(__dirname, filePath);
      if (fs.existsSync(fullPath)) {
        try { fs.unlinkSync(fullPath); } catch (e) { console.warn('Failed to remove file:', e.message); }
      }
    } else if (filePath) {
      console.warn('Skipping deletion for unknown file path pattern for content delete', { contentId, filePath });
    }

    // Log activity
    const ctype = rows[0].content_type;
    logActivity({
      userId: req.user.id,
      projectId,
      sceneId,
      shotId,
      shotContentId: contentId,
      activity: `Deleted ${ctype || 'content'} from shot ${shotId}`
    });

    res.json({ message: 'Shot content deleted successfully' });
  } catch (err) {
    console.error('Error deleting shot content:', err);
    res.status(500).json({ message: 'Failed to delete shot content' });
  }
});

// Update notes for a content item
app.put('/api/projects/:projectId/scenes/:sceneId/shots/:shotId/content/:contentId', authMiddleware, async (req, res) => {
  const { projectId, sceneId, shotId, contentId } = req.params;
  const { notes } = req.body || {};

  try {
    // Verify ownership and existence
    const [rows] = await pool.query(
      `SELECT sc.id FROM shot_content sc
       JOIN shots sh ON sc.shot_id = sh.id
       JOIN scenes s ON sh.scene_id = s.id
       JOIN projects p ON s.project_id = p.id
       WHERE sc.id = ? AND sc.shot_id = ? AND sh.scene_id = ? AND s.project_id = ? AND p.user_id = ?`,
      [contentId, shotId, sceneId, projectId, req.user.id]
    );

    if (rows.length === 0) {
      return res.status(404).json({ message: 'Content not found or access denied' });
    }

    const safeNotes = notes && notes.length > 0 ? String(notes).substring(0, 255) : null;
    await pool.query(
      'UPDATE shot_content SET notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [safeNotes, contentId]
    );

    // Log activity
    logActivity({
      userId: req.user.id,
      projectId,
      sceneId,
      shotId,
      shotContentId: contentId,
      activity: 'Updated shot content notes'
    });

    res.json({ message: 'Content notes updated successfully', notes: safeNotes });
  } catch (err) {
    console.error('Error updating content notes:', err);
    res.status(500).json({ message: 'Failed to update content notes' });
  }
});

const PORT = process.env.API_PORT || 3001;
app.listen(PORT, () => {
  console.log(`API server running on port ${PORT}`);
});
