import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useTheme } from '../contexts/ThemeContext';

export default function Navbar() {
  const [user, setUser] = useState(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const dropdownRef = useRef(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (res.ok) {
          const data = await res.json();
          setUser(data);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    };

    fetchUser();
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  const handleLogout = async () => {
    // Close dropdown immediately for better UX
    setIsMenuOpen(false);

    try {
      // Call logout API to clear server session
      await fetch('http://localhost:3001/api/logout', {
        method: 'POST',
        credentials: 'include'
      });
    } catch (error) {
      console.error('Logout error:', error);
    }

    // Always clear local state and redirect regardless of API response
    setUser(null);
    router.push('/');
  };

  const isAuthPage = router.pathname === '/login' || router.pathname === '/register' || router.pathname === '/';

  if (isAuthPage) return null;

  return (
    <nav className="navbar">
      <div className="navbar-content">
        <div className="logo">
          <i className="fa-solid fa-clapperboard" style={{ marginRight: 8 }}></i>
          Creative Storyboard
        </div>

        {user && (
            <div className="nav-menu">
              <ul>
                <li>
                  <Link href="/home" className={router.pathname === '/home' ? 'active' : ''}>
                    <i className="fa-solid fa-house" style={{ marginRight: 6 }}></i> Home
                  </Link>
                </li>
                <li>
                  <Link href="/profile" className={router.pathname === '/profile' ? 'active' : ''}>
                    <i className="fa-solid fa-user" style={{ marginRight: 6 }}></i> Profile
                  </Link>
                </li>
                <li>
                  <Link href="/projects" className={router.pathname === '/projects' ? 'active' : ''}>
                    <i className="fa-solid fa-folder" style={{ marginRight: 6 }}></i> Projects
                  </Link>
                </li>
              </ul>

              <div className="user-menu" ref={dropdownRef}>
                <div className="user-avatar" onClick={() => setIsMenuOpen(!isMenuOpen)}>
                  {user.username ? user.username.charAt(0).toUpperCase() : '?'}
                </div>

                {isMenuOpen && (
                  <div className="dropdown-menu">
                    <div className="dropdown-item">
                      <span className="user-info">
                        <strong>{user.username}</strong>
                        <small>{user.email}</small>
                      </span>
                    </div>
                    <div className="dropdown-divider"></div>
                    <button className="dropdown-item" onClick={() => router.push('/profile')}>
                      <i className="fa-solid fa-gear" style={{ marginRight: 8 }}></i> Settings
                    </button>
                    <button className="dropdown-item" onClick={() => router.push('/help')}>
                      <i className="fa-regular fa-circle-question" style={{ marginRight: 8 }}></i> Help
                    </button>
                    <div className="dropdown-divider"></div>
                    <button className="dropdown-item logout-btn" onClick={handleLogout}>
                      <i className="fa-solid fa-right-from-bracket" style={{ marginRight: 8 }}></i> Logout
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
      </div>
    </nav>
  );
}
