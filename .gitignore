# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
uploads/

# Audio library: ignore binary media under collection/audio, keep text/json indexes
collection/audio/**/*.mp3
collection/audio/**/*.wav
collection/audio/**/*.ogg
collection/audio/**/*.m4a
collection/audio/**/*.aac
collection/audio/**/*.flac
collection/audio/**/*.webm
collection/audio/**/*.aiff
collection/audio/**/*.aif
collection/audio/**/*.wma
