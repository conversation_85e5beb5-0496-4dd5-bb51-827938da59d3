@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

/* Smooth theme transitions */
*,
*::before,
*::after {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

:root {
  /* Light Theme Variables */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-color: #667eea;
  --accent-hover: #5a67d8;
  --accent-text: rgba(255, 255, 255, 0.95);
  --glass-bg: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
  --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.5);
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-light: rgba(255, 255, 255, 0.9);
  --text-muted: rgba(255, 255, 255, 0.7);
  --bg-primary: #667eea;
  --bg-secondary: #764ba2;
}

[data-theme="dark"] {
  /* Dark Theme Variables */
  --primary-gradient: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2d2d2d 100%);
  --secondary-gradient: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  --accent-color: #63b3ed;
  --accent-hover: #4299e1;
  --accent-text: #63b3ed;
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.5);
  --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.7);
  --text-primary: #e2e8f0;
  --text-secondary: #cbd5e0;
  --text-light: rgba(255, 255, 255, 0.95);
  --text-muted: rgba(255, 255, 255, 0.6);
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Poppins', sans-serif;
  background: var(--primary-gradient);
  background-attachment: fixed;
  color: var(--text-primary);
  overflow-x: hidden;
  position: relative;
}

/* Animated background particles */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 177, 153, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

[data-theme="dark"] body::before {
  background:
    radial-gradient(circle at 20% 80%, rgba(30, 30, 30, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(45, 45, 45, 0.4) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(25, 25, 25, 0.3) 0%, transparent 50%);
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  position: relative;
}

.card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  padding: 50px 40px;
  box-shadow: var(--shadow-light);
  max-width: 450px;
  width: 100%;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideUp 0.6s ease-out;
}

.card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.card:hover::after {
  animation: glideAngled 0.8s ease-out forwards;
}

@keyframes glideAngled {
  0% {
    left: -150%;
    opacity: 1;
  }
  100% {
    left: 150%;
    opacity: 0;
  }
}

/* Removed old ::before animation - using ::after glass reflection instead */

.card:hover {
  box-shadow: var(--shadow-heavy);
  border-color: rgba(255, 255, 255, 0.3);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Navigation Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  padding: 15px 30px;
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Removed navbar-actions styles - no longer needed */

.logo {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-text);
}

nav ul {
  list-style: none;
  padding: 0;
  display: flex;
  justify-content: center;
  gap: 30px;
  margin: 0;
}

nav a {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  padding: 10px 18px;
  border-radius: 12px;
  transition: all 0.2s ease;
  display: block;
  border: 1px solid transparent;
}

nav a:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
}

nav a.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
}

/* Form Elements */
.form-group {
  position: relative;
  margin-bottom: 25px;
}

input {
  width: 100%;
  padding: 15px 20px;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  font-size: 16px;
  color: var(--text-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: inherit;
}

input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] input {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
}

[data-theme="dark"] input:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
}

button {
  /* width: 100%; */
  padding: 15px 20px;
  border-radius: 8px;
  border: none;
  background: var(--accent-color);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  margin-top: 20px;
  font-family: inherit;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

button:hover {
  background: var(--accent-hover);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

button:active {
  opacity: 0.9;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: var(--text-light);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Typography */
h1 {
  margin-bottom: 20px;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--accent-text);
  text-align: center;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.3)); }
  to { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6)); }
}

h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-light);
  margin-bottom: 15px;
}

h3 {
  font-size: 1.4rem;
  font-weight: 500;
  color: var(--text-light);
  margin-bottom: 10px;
}

p {
  margin: 15px 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-light { color: var(--text-light); }
.mb-20 { margin-bottom: 20px; }
.mt-20 { margin-top: 20px; }

/* Loading Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Message Styles */
.message {
  padding: 15px 20px;
  border-radius: 10px;
  margin: 20px 0;
  font-weight: 500;
  animation: fadeIn 0.5s ease-out;
}

.message.success {
  background: rgba(72, 219, 251, 0.2);
  border: 1px solid rgba(72, 219, 251, 0.3);
  color: #48dbfb;
}

.message.error {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Dashboard Cards */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  max-width: 1200px;
  width: 100%;
}

.dashboard-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  padding: 30px;
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.dashboard-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.dashboard-card:hover::after {
  animation: glideAngled 0.8s ease-out forwards;
}

/* Removed rotating conic-gradient animation - keeping only glass reflection */

.dashboard-card:hover {
  box-shadow: var(--shadow-heavy);
}

/* Removed rotate keyframe - no longer needed */

/* Profile Avatar */
.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  font-weight: 700;
  color: white;
  margin: 0 auto 20px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--accent-color);
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
}

.fab:hover {
  background: var(--accent-hover);
  transform: scale(1.1) rotate(90deg);
  box-shadow: var(--shadow-heavy);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.modal {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  padding: 40px;
  max-width: 645px;
  min-width: 645px;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

/* Tooltip */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  text-align: center;
  border-radius: 6px;
  padding: 5px 10px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 14px;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Enhanced Navigation Styles */
.nav-menu {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nav-menu ul {
  display: flex;
  gap: 15px;
  margin: 0;
  list-style: none;
  padding: 0;
}

.nav-menu li {
  margin: 0;
}

/* Removed standalone theme toggle styles - now using dropdown in user menu */

.user-menu {
  position: relative;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 10px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 10px 0;
  min-width: 200px;
  box-shadow: 0 15px 35px rgba(31, 38, 135, 0.5), 0 5px 15px rgba(0, 0, 0, 0.1);
  animation: slideDown 0.3s ease-out;
  z-index: 1001;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 12px 20px;
  background: none;
  border: none;
  border-radius: 0;
  color: var(--text-light);
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0;
}

.dropdown-item.logout-btn {
  color: #ff6b6b;
}

.dropdown-item.logout-btn:hover {
  background: rgba(255, 107, 107, 0.1);
}

.dropdown-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 8px 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-info small {
  opacity: 0.7;
  font-size: 12px;
}

/* Theme Selector in Dropdown */
.theme-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px !important;
  cursor: default;
}

.theme-selector:hover {
  background: none !important;
}

.theme-selector-label {
  color: var(--text-light);
  font-size: 14px;
  font-weight: 500;
}

.theme-dropdown {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: var(--text-light);
  font-size: 12px;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  min-width: 90px;
}

.theme-dropdown:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.theme-dropdown:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.theme-dropdown option {
  background: var(--bg-primary);
  color: var(--text-light);
  padding: 8px;
}

[data-theme="dark"] .theme-dropdown {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .theme-dropdown:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .theme-dropdown option {
  background: #1a1a1a;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar-content {
    padding: 0 20px;
  }

  .nav-menu ul {
    display: none;
  }

  .container {
    padding-top: 80px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .card {
    padding: 30px 25px;
  }
}

/* Profile Page Styles */
.profile-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.profile-header {
  text-align: center;
  margin-bottom: 40px;
}

.user-email {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  margin-bottom: 30px;
}

.profile-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-text);
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.profile-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

@media (max-width: 768px) {
  .profile-content {
    grid-template-columns: 1fr;
  }
}

.profile-section {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  padding: 30px;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.profile-section::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.profile-section:hover::after {
  animation: glideAngled 0.8s ease-out forwards;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group label {
  display: block;
  color: var(--text-light);
  font-weight: 500;
  margin-bottom: 8px;
}

.form-group textarea {
  width: 100%;
  padding: 15px 20px;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  font-size: 16px;
  color: var(--text-light);
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
}

.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .form-group textarea {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
}

[data-theme="dark"] .form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.profile-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-item strong {
  color: var(--text-light);
  display: block;
  margin-bottom: 5px;
}

.info-item p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
}

.info-item a {
  color: #48dbfb;
  text-decoration: none;
}

.info-item a:hover {
  text-decoration: underline;
}

/* Settings Toggle Switch */
.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item:last-child {
  border-bottom: none;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background: var(--accent-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Profile Theme Dropdown */
.profile-theme-dropdown {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: var(--text-light);
  font-size: 14px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
  min-width: 120px;
}

.profile-theme-dropdown:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.profile-theme-dropdown:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.profile-theme-dropdown option {
  background: var(--bg-primary);
  color: var(--text-light);
  padding: 8px;
}

[data-theme="dark"] .profile-theme-dropdown {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .profile-theme-dropdown:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .profile-theme-dropdown:focus {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .profile-theme-dropdown option {
  background: #1a1a1a;
  color: var(--text-light);
}

/* Authentication Pages Styles */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.auth-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

.floating-particle {
  position: fixed;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  pointer-events: none;
  animation: floatUp 5s linear infinite;
  z-index: -1;
}

[data-theme="dark"] .floating-particle {
  background: rgba(255, 255, 255, 0.3);
}

@keyframes floatUp {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.auth-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  border: 1px solid var(--glass-border);
  padding: 50px 40px;
  box-shadow: var(--shadow-heavy);
  max-width: 675px;
  min-width: 675px;
  text-align: center;
  position: relative;
  overflow: hidden;
  animation: slideUp 0.8s ease-out;
  transition: all 0.3s ease;
}

.auth-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.auth-card:hover::after {
  animation: glideAngled 0.8s ease-out forwards;
}

.auth-header {
  margin-bottom: 40px;
}

.auth-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 20px;
  font-size: 18px;
  z-index: 1;
}

.auth-input {
  width: 100%;
  padding: 18px 20px 18px 55px;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  font-size: 16px;
  color: var(--text-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: inherit;
}

.auth-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
}

.password-toggle {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: auto;
  margin: 0;
}

.password-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.auth-button {
  width: 100%;
  padding: 18px 20px;
  border-radius: 15px;
  border: none;
  background: var(--accent-color);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  margin-top: 10px;
  font-family: inherit;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.auth-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.auth-button:not(:disabled):hover {
  background: var(--accent-hover);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.auth-footer {
  margin-top: 30px;
  text-align: center;
}

.auth-footer p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10px;
}

.auth-link {
  color: #48dbfb;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
}

.auth-link:hover {
  color: #0be881;
  text-decoration: underline;
}

.forgot-link {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s ease;
}

.forgot-link:hover {
  color: var(--text-light);
}

/* Registration Progress Bar */
.progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  padding: 0 20px;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.step-circle.active {
  background: var(--accent-color);
  border-color: transparent;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.progress-step span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.progress-line {
  width: 60px;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 10px;
}

.step-content {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-buttons {
  display: flex;
  gap: 15px;
}

.step-buttons .btn-secondary,
.step-buttons .auth-button {
  flex: 1;
}

/* Password Strength Indicator */
.password-strength {
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.password-strength span {
  font-size: 12px;
  font-weight: 500;
  min-width: 60px;
  text-align: right;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .auth-card {
    padding: 40px 30px;
    margin: 20px;
    max-width: 90%;
  }

  .progress-bar {
    padding: 0 10px;
  }

  .progress-line {
    width: 40px;
  }

  .step-buttons {
    flex-direction: column;
  }

  .profile-stats {
    flex-direction: column;
    gap: 20px;
  }

  .settings-grid {
    gap: 15px;
  }
}

/* Dashboard Home Page Styles */
.dashboard-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  border: 1px solid var(--glass-border);
  padding: 40px;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.welcome-header::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.welcome-header:hover::after {
  animation: glideAngled 0.9s ease-out forwards;
}

.welcome-content h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  animation: glow 3s ease-in-out infinite alternate;
}

.welcome-content p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

.current-time {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-text);
}

.current-date {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

.weather-icon {
  font-size: 3rem;
  animation: bounce 3s ease-in-out infinite;
}

.weather-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.temperature {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-light);
}

.weather-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Stats Cards */
.stats-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  min-height: 120px;
}

.card-icon {
  font-size: 3rem;
  opacity: 0.8;
}

.card-content h3 {
  margin: 0 0 10px 0;
  color: var(--text-light);
  font-size: 1rem;
  font-weight: 500;
}

.card-content .stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--accent-text);
  line-height: 1;
  margin-bottom: 5px;
}

.stat-change {
  color: #0be881;
  font-size: 0.85rem;
  font-weight: 500;
  margin: 0;
}

/* Action Cards */
.action-card {
  padding: 30px;
}

.card-header {
  margin-bottom: 25px;
}

.card-header h3 {
  margin: 0;
  color: var(--text-light);
  font-size: 1.2rem;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  color: var(--text-light);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin: 0;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 1.2rem;
}

/* Recent Activity */
.recent-card {
  padding: 30px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.activity-icon {
  font-size: 1.5rem;
  opacity: 0.8;
}

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 0 0 5px 0;
  color: var(--text-light);
  font-size: 0.95rem;
}

.activity-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

/* Progress Card */
.progress-card {
  padding: 30px;
}

.progress-items {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-light);
  font-weight: 500;
}

.progress-bar-container {
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: var(--accent-color);
  border-radius: 4px;
  transition: width 0.8s ease;
  animation: progressGlow 2s ease-in-out infinite alternate;
}

@keyframes progressGlow {
  0% { box-shadow: 0 0 5px rgba(72, 219, 251, 0.5); }
  100% { box-shadow: 0 0 20px rgba(72, 219, 251, 0.8); }
}

/* Landing Page Styles */
.landing-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.landing-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.landing-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  animation: float 8s ease-in-out infinite;
}

.landing-shape.shape-1 {
  width: 300px;
  height: 300px;
  top: 5%;
  left: 5%;
  animation-delay: 0s;
}

.landing-shape.shape-2 {
  width: 200px;
  height: 200px;
  top: 20%;
  right: 10%;
  animation-delay: 2s;
}

.landing-shape.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 30%;
  left: 70%;
  animation-delay: 4s;
}

.landing-shape.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 10%;
  right: 30%;
  animation-delay: 6s;
}

.landing-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 30px;
  border: 1px solid var(--glass-border);
  padding: 60px 50px;
  box-shadow: var(--shadow-heavy);
  max-width: 900px;
  width: 100%;
  text-align: center;
  position: relative;
  overflow: hidden;
  animation: slideUp 1s ease-out;
  transition: all 0.3s ease;
}

.landing-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.25), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.landing-card:hover::after {
  animation: glideAngled 1s ease-out forwards;
}

.landing-header {
  margin-bottom: 50px;
}

.logo-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.logo-icon {
  font-size: 4rem;
  animation: bounce 3s ease-in-out infinite;
}

.landing-subtitle {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 20px;
  font-weight: 600;
}

.landing-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Features Showcase */
.features-showcase {
  margin: 50px 0;
  padding: 40px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.feature-display {
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease-out;
}

.feature-display .feature-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.feature-display h3 {
  font-size: 1.8rem;
  color: var(--text-light);
  margin-bottom: 15px;
}

.feature-display p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
}

.feature-dots {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: var(--accent-color);
  transform: scale(1.3);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

/* CTA Section */
.cta-section {
  margin: 50px 0;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 40px;
}

.cta-primary, .cta-secondary {
  /* padding: 18px 35px; */
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.btn-medium {
  padding: 18px 35px;
}

.cta-primary {
  background: var(--accent-color);
  color: white;
  border: none;
}

.cta-primary:hover {
  background: var(--accent-hover);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.cta-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.cta-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Social Proof */
.social-proof {
  margin-top: 40px;
}

.stats-row {
  display: flex;
  justify-content: center;
  gap: 50px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-text);
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
  margin-top: 50px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 30px 25px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.25), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.feature-card:hover::after {
  animation: glideAngled 0.8s ease-out forwards;
}

/* Removed old ::before animation - using ::after glass reflection instead */

.feature-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.feature-card .feature-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  opacity: 0.9;
}

.feature-card h4 {
  color: var(--text-light);
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .landing-card {
    padding: 40px 30px;
    margin: 20px;
  }

  .logo-animation {
    flex-direction: column;
    gap: 10px;
  }

  .logo-icon {
    font-size: 3rem;
  }

  .landing-subtitle {
    font-size: 1.3rem;
  }

  .landing-description {
    font-size: 1rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-primary, .cta-secondary {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .stats-row {
    gap: 30px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .welcome-header {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .stats-row {
    flex-direction: column;
    gap: 20px;
  }

  .dashboard-grid {
    gap: 20px;
  }

  .dashboard-card {
    padding: 20px;
  }

  .fab {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
}

/* Modal Header Styles */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  margin: 0;
  color: var(--text-light);
  font-size: 1.5rem;
}

.modal-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: auto;
  margin: 0;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  transform: scale(1.1);
}

.modal-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

/* Tooltip Positioning */
.tooltip-top .tooltiptext {
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
}

.tooltip-bottom .tooltiptext {
  top: 125%;
  left: 50%;
  margin-left: -60px;
}

.tooltip-left .tooltiptext {
  top: -5px;
  right: 125%;
  margin-left: 0;
}

.tooltip-right .tooltiptext {
  top: -5px;
  left: 125%;
  margin-left: 0;
}

/* Success and Error States */
.success-state {
  color: #0be881;
  animation: successPulse 0.6s ease-out;
}

.error-state {
  color: #ff6b6b;
  animation: errorShake 0.6s ease-out;
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
*:focus {
  outline: 2px solid rgba(72, 219, 251, 0.5);
  outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(72, 219, 251, 0.3);
}

/* Print Styles */
@media print {
  .navbar,
  .fab,
  .floating-particle,
  .auth-background,
  .landing-background {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .card,
  .dashboard-card,
  .auth-card,
  .landing-card {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
}

/* Projects Page Styles */
.projects-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.projects-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  padding: 30px;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.projects-header::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.projects-header:hover::after {
  animation: glideAngled 0.8s ease-out forwards;
}

.projects-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
}

.projects-header p {
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

.projects-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  border: 1px solid var(--glass-border);
  padding: 25px;
  box-shadow: var(--shadow-light);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
}

.stat-card:hover::after {
  animation: glideAngled 0.8s ease-out forwards;
}

.stat-card:hover {
  box-shadow: var(--shadow-heavy);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.stat-info .stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-text);
  line-height: 1;
}

.stat-info .stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 500;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
}

.project-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  height: 380px; /* Fixed height for all cards */
  display: flex;
  flex-direction: column;
}

.project-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -150%;
  width: 100%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-20deg);
  pointer-events: none;
  opacity: 0;
  z-index: 1;
}

.project-card:hover::after {
  animation: glideAngled 0.8s ease-out forwards;
}

.project-card:hover {
  box-shadow: var(--shadow-heavy);
}

.project-thumbnail {
  height: 150px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.project-icon {
  font-size: 4rem;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  opacity: 0;
  transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-card:hover .project-icon {
  transform: scale(1.1);
}

.overlay-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;
}

.overlay-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.project-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.project-header h3 {
  margin: 0;
  color: var(--text-light);
  font-size: 1.3rem;
  font-weight: 600;
}

.project-status {
  padding: 4px 12px;
  border-radius: 5px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  width: fit-content;
  flex-shrink: 0;
}

.project-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 20px;
  flex: 1;
  /* Truncate text to 3 lines with ellipsis */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  /* Fallback for browsers that don't support -webkit-line-clamp */
  max-height: calc(1.5em * 3); /* 3 lines * line-height */
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto; /* Push to bottom */
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.meta-icon {
  opacity: 0.8;
}

.project-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.project-form select {
  width: 100%;
  padding: 15px 20px;
  border-radius: 15px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  font-size: 16px;
  color: var(--text-light);
  font-family: inherit;
}

.project-form select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .project-form select {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
}

[data-theme="dark"] .project-form select:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Image Upload Styles */
.image-upload-container {
  position: relative;
}

.image-input {
  display: none;
}

.image-upload-label {
  display: block;
  width: 100%;
  min-height: 200px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.image-upload-label:hover {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.1);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.upload-placeholder span:not(.upload-icon) {
  font-size: 16px;
  margin-bottom: 5px;
}

.upload-placeholder small {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 5px;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 200px;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 13px;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 13px;
  color: white;
  font-size: 14px;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

/* Project Image Styles */
.project-image, .project-pattern {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px 15px 0 0;
}

.project-pattern {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: rgba(255, 255, 255, 0.8);
}

/* Empty State Styles */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
  font-size: 72px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 24px;
  margin-bottom: 10px;
  color: var(--text-light);
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 30px;
  color: rgba(255, 255, 255, 0.6);
}

/* Project Status Styles */
.project-status.in-progress {
  background-color: #48dbfb !important;
}

/* Project Detail Page Styles */
.project-detail-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.project-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 12px 20px;
  color: var(--text-light);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateX(-2px);
}

.project-actions {
  display: flex;
  gap: 15px;
}

.project-banner {
  position: relative;
  height: 400px;
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 40px;
  box-shadow: var(--shadow-heavy);
}

.banner-image, .banner-pattern {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-pattern {
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 40px;
  color: white;
}

.project-info h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 15px 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.project-meta-info {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.meta-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.project-detail-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.content-section {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  padding: 30px;
  box-shadow: var(--shadow-light);
}

.content-section h2 {
  color: var(--text-light);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
}

.project-description-full {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

.empty-scenes {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.empty-scenes .empty-icon {
  font-size: 72px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-scenes h3 {
  font-size: 24px;
  margin-bottom: 10px;
  color: var(--text-light);
}

.empty-scenes p {
  font-size: 16px;
  margin-bottom: 30px;
  color: rgba(255, 255, 255, 0.6);
}

.scenes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 25px;
}

/* Scene Card Styles */
.scene-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  border: 1px solid var(--glass-border);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  position: relative;
}

.scene-card:hover {
  box-shadow: var(--shadow-heavy);
  transform: translateY(-2px);
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
}

.scene-actions {
  display: flex;
  gap: 8px;
}

.scene-action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.scene-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.scene-action-btn.delete:hover {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.4);
  color: #ff6b6b;
}

.scene-content {
  padding: 20px;
}

.scene-content h4 {
  color: var(--text-light);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.scene-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 20px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.scene-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.scene-footer {
  padding: 0 20px 20px 20px;
}

.scene-view-btn {
  width: 100%;
  padding: 12px;
  border-radius: 10px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.error-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.error-state h2 {
  color: var(--text-light);
  margin-bottom: 20px;
}

/* Responsive Design for Project Detail */
@media (max-width: 768px) {
  .project-detail-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .project-banner {
    height: 250px;
  }

  .banner-overlay {
    padding: 20px;
  }

  .project-info h1 {
    font-size: 1.8rem;
  }

  .project-meta-info {
    flex-direction: column;
    gap: 10px;
  }

  .content-section {
    padding: 20px;
  }
}

/* Shots Page Styles */
.shots-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
}

.shots-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1;
}

.breadcrumb-link {
  background: none;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
  font-size: 14px;
  line-height: 1;
  padding: 0;
  margin: 0;
  display: inline-flex;
  align-items: center;
}

.breadcrumb-link:hover {
  color: var(--accent-hover);
  text-decoration: underline;
}

.breadcrumb-separator {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  height: 14px;
}

.breadcrumb-current {
  color: var(--text-light);
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  display: inline-flex;
  align-items: center;
}

.scene-info-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
  padding: 30px;
  margin-bottom: 40px;
  box-shadow: var(--shadow-light);
}

.scene-info-card h1 {
  color: var(--text-light);
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 15px 0;
}

.scene-info-card .scene-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
}

.scene-info-card .scene-meta {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.shots-section h2 {
  color: var(--text-light);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 25px 0;
}

.empty-shots {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.7);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid var(--glass-border);
}

.empty-shots .empty-icon {
  font-size: 72px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-shots h3 {
  font-size: 24px;
  margin-bottom: 10px;
  color: var(--text-light);
}

.empty-shots p {
  font-size: 16px;
  margin-bottom: 30px;
  color: rgba(255, 255, 255, 0.6);
}

.shots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 25px;
}

/* Shot Card Styles */
.shot-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  border: 1px solid var(--glass-border);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  position: relative;
}

.shot-card:hover {
  box-shadow: var(--shadow-heavy);
  transform: translateY(-2px);
}

.shot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
}

.shot-number {
  background: var(--accent-color);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.shot-actions {
  display: flex;
  gap: 8px;
}

.shot-action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shot-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.shot-action-btn.delete:hover {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.4);
  color: #ff6b6b;
}

.shot-content {
  padding: 20px;
}

.shot-content h4 {
  color: var(--text-light);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.shot-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ai-prompt-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 3px solid var(--accent-color);
}

.ai-prompt-section label {
  color: var(--accent-color);
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  display: block;
}

.ai-prompt {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
  font-style: italic;
}

.shot-meta {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* Responsive Design for Shots */
@media (max-width: 768px) {
  .shots-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .breadcrumb {
    justify-content: center;
  }

  .shots-grid {
    grid-template-columns: 1fr;
  }

  .scene-info-card {
    padding: 20px;
  }

  .scene-info-card h1 {
    font-size: 1.5rem;
  }
}
